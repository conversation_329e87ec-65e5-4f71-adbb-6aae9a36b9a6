<?php

namespace App\Repositories\Home;

use Illuminate\Support\Facades\DB;


class ProductRepository
{
    /**
     * 取得分館商品資料（含 webtype_keywords, webtype_description）
     * @param int|null $id
     * @param int|null $searchPrev
     * @return object|null
     */
    public function getProductLayerByIdOrSearchPrev($id = null, $searchPrev = null)
    {
        $productQuery = DB::table('product')->select(
            'id',
            'title',
            'inner_adv01_pic',
            'inner_adv01_link',
            'inner_adv02_pic',
            'inner_adv02_link',
            'webtype_keywords',
            'online',
            'webtype_description'
        );

        if ($id) {
            if (!is_numeric($id)) {
                return null;
            }
            return $productQuery->find($id);
        } else {
            return $productQuery->whereRaw('online=1')->whereRaw('id =' . $searchPrev)->first();
        }
    }

    /**
     * 依 id 取得單一分館資料
     * @param int $id
     * @return object|null
     */
    public function getProductById($id)
    {
        return DB::table('product')->where('id', $id)->first();
    }
/**
     * 取得隨機推薦分館
     * @param int $rid
     * @return \Illuminate\Support\Collection
     */
    public function getRandADV($rid)
    {
        return DB::table('product')
            ->select('id', 'title', 'inner_adv01_pic', 'inner_adv01_link', 'content', 'pic', 'webtype_keywords', 'webtype_description')
            ->whereRaw('online = 1 AND id <> ' . $rid)
            ->orderByRaw('RAND()')
            ->limit(2)
            ->get();
    }
}
