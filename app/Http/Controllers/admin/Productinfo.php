<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use GuzzleHttp\Client;

use App\Services\CommonService;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\ProductHelpler;
use App\Services\pattern\ExaminationHelper;
use App\Services\pattern\BonusSettingHelper;
use App\Services\pattern\MemberInstance;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Productinfo extends MainController
{
    private $resTableName;
    private $DBTextConnecter;
    private $DBFileConnecter;

    const PER_PAGE_ROWS = 10;
    const SIMPLE_MODE_PAGINATE = false;

    public function __construct()
    {
        parent::__construct();
        $this->resTableName = 'productinfo';
        $this->DBTextConnecter = DBTextConnecter::withTableName('productinfo');
        $this->DBFileConnecter = DBFileConnecter::withTableName('productinfo');
    }

    /* 展示庫存編碼 */
    public function show_position_portion(Request $request)
    {
        $get = $request->query();

        $product_id = $get['searchKey'] ?? '';
        $datetime = $get['datetime'] ?? '';

        if ($datetime != '') {
            $position_portion = DB::table('position_portion as Pp')
                ->select('Pp.*', 'pos.name', 'pos.number', 'pt.title', 'pos.max')
                ->join('position as pos', 'Pp.position_id', 'pos.id')
                ->leftJoin('productinfo_type as pt', 'Pp.productinfo_type', 'pt.id')
                ->whereRaw("Pp.product_id = '" . $product_id . "' and Pp.num != '0' and Pp.datetime = '" . $datetime . "' ")
                ->orderByRaw('pt.order_id asc, pt.id asc, Pp.position_number asc')
                ->get();
        } else {
            $position_portion = DB::table('position_portion as Pp')
                ->select('Pp.*', 'pos.name', 'pos.number', 'pt.title', 'pos.max')
                ->join('position as pos', 'Pp.position_id', 'pos.id')
                ->leftJoin('productinfo_type as pt', 'Pp.productinfo_type', 'pt.id')
                ->whereRaw("Pp.product_id = '" . $product_id . "' and Pp.num != '0' ")
                ->orderByRaw('pt.order_id asc, pt.id asc, Pp.position_number asc')
                ->get();
        }
        $position_portion = CommonService::objectToArray($position_portion);
        $searchName = DB::table('productinfo')->select('title')->find($product_id)->title;
        // dump($position_portion); exit;

        $show_list = [];
        $number = '';
        foreach ($position_portion as $pk => $pv) {
            if ($pv['max'] == 1)
                $pv['number'] = 0; // 如果是無限，則左側不補零
            $data = [
                'position_code' => $pv['name'] . str_pad($pv['position_number'], strlen($pv['number']), '0', STR_PAD_LEFT),
                'p_type'        => $pv['title'],
                'num'           => $pv['num']
            ];
            array_push($show_list, $data);
        }
        // dump($show_list);exit;

        $this->data['searchKey'] = $product_id;
        $this->data['searchName'] = $searchName;
        $this->data['show_list_app'] = $show_list;

        return view('admin.productinfo.show_position_portion', ['data' => $this->data]);
    }

    /* 利用條碼判斷新增or編輯商品 */
    public function input_isbn(Request $request)
    { //ISBN查詢  2019/12/18
        $ISBN = $request->post('ISBN');
        $ISBN = DB::table('productinfo')->select('id')
            ->whereRaw($this->distributor_id_where_sql)
            ->where("ISBN", $ISBN ?? '')
            ->where("ISBN", '!=', "")
            ->first();
        $ISBN = CommonService::objectToArray($ISBN);
        return $ISBN ? $ISBN['id'] : '';
    }

    /* 回傳商品顯示位置 */
    public static function show_array(Request $request)
    { //多階層查詢  2019/12/17
        $array = $request->post('array') ?? '[]';
        $type = $request->post('type') ?? 'text';
        return ProductHelpler::show_array($array, $type);
    }

    /**
     * 新增產品資訊
     */
    public function allcreate(Request $request)
    {
        /* 指派資料 */
        $this->assign_data();

        $ISBN = "";
        if (!empty($request->get('ISBN'))) {
            $ISBN = $request->get('ISBN');
        }

        $final_array = '[]';

        // 根據分類樹點擊的位置預設階層
        $prev = $request->get('prev_id') ?? '';
        $parent_id = $request->get('parent_id') ?? '';
        $branch_id = $request->get('branch_id') ?? '';
        if ($prev != '') {
            $product = DB::table('product')->find($prev);
            if ($product) {
                if (($this->admin_type == 'admin' && $product->distributor_id == 0) ||
                    ($this->admin_type == 'distribution' && $product->distributor_id == session()->get($this->admin_type)['id'])
                ) {
                    $final_array = '[{"prev_id":"' . $prev . '","branch_id":"0","parent_id":"0"}]';
                }
            }
        }
        if ($parent_id != '') {
            $prev = DB::table('typeinfo')->selectRaw('parent_id,distributor_id')->find($parent_id);
            $prev = CommonService::objectToArray($prev);
            if ($prev) {
                if (($this->admin_type == 'admin' && $prev['distributor_id'] == 0) ||
                    ($this->admin_type == 'distribution' && $prev['distributor_id'] == session()->get($this->admin_type)['id'])
                ) {
                    $final_array = '[{"prev_id":"' . $prev['parent_id'] . '","branch_id":"' . $parent_id . '","parent_id":"' . $parent_id . '"}]';
                }
            }
        }
        if ($branch_id != '') {
            $parent_id = DB::table('typeinfo')->select('id', 'parent_id', 'branch_id', 'distributor_id')->find($branch_id);
            while ($parent_id->branch_id > 0) {
                $parent_id = DB::table('typeinfo')->select('id', 'parent_id', 'branch_id', 'distributor_id')->find($parent_id['branch_id']);
            }
            $parent_id = CommonService::objectToArray($parent_id);
            if ($parent_id) {
                if (($this->admin_type == 'admin' && $parent_id['distributor_id'] == 0) ||
                    ($this->admin_type == 'distribution' && $parent_id['distributor_id'] == session()->get($this->admin_type)['id'])
                ) {
                    $final_array = '[{"prev_id":"' . $parent_id['parent_id'] . '","branch_id":"' . $branch_id . '","parent_id":"' . $parent_id['id'] . '"}]';
                }
            }
        }

        // 根據上次建立的商品預設階層
        if ($final_array == '[]') {
            $pre_prod = DB::table('productinfo')->whereRaw($this->distributor_id_where_sql)->orderBy('id', 'desc')->first();
            $pre_prod = CommonService::objectToArray($pre_prod);
            if (empty($pre_prod) == false) {
                $final_array = $pre_prod['final_array'];
            }
        }

        $this->data['final_array'] = $final_array;

        /*編輯畫面帶入預設資料*/
        $productinfo['id'] = 0;
        $productinfo['pic'] = [];
        $productinfo['title'] = '';

        $productinfo['r_repeat'] = "";
        $productinfo['final_array'] = $final_array;
        $productinfo['ISBN'] = $ISBN;
        $productinfo['distributor_id'] = '';
        $productinfo['kol_id'] = "";
        $productinfo['out_ID'] = "";
        $productinfo['product_id'] = "";

        $productinfo['Author'] = "";
        $productinfo['house'] = "";
        $productinfo['house_date'] = "";

        $productinfo['prodesc'] = "";
        $productinfo['keywords'] = "";
        $productinfo['display'] = "";
        $productinfo['content'] = "";

        $productinfo['pre_buy'] = "0";
        $productinfo['pre_buy_limit'] = "0";
        $productinfo['card_pay'] = "1";
        $productinfo['is_registrable'] = "0";
        $productinfo['is_roll_call'] = "0";
        $productinfo['register_data_change_limit'] = "";
        $productinfo['remind_msg'] = "";

        $productinfo['expiring_product'] = 0;
        $productinfo['hot_product'] = 0;
        $productinfo['recommend_product'] = 0;
        $productinfo['spe_price_product'] = 0;

        $productinfo['ask_price'] = "0";
        $productinfo['has_price'] = "1";
        $productinfo['items'] = "[]";

        $productinfo['pushitem'] = "";

        $productinfo['text1_online'] = "";
        $productinfo['text1'] = "";
        $productinfo['text2_online'] = "";
        $productinfo['text2'] = "";
        $productinfo['text3_online'] = "";
        $productinfo['text3'] = "";
        $productinfo['text4_online'] = "";
        $productinfo['text4'] = "";
        $productinfo['text5_online'] = "";
        $productinfo['text5'] = "";

        $productinfo['shipping_fee_tag'] = 0;

        /*天脈積分計算欄位*/
        $productinfo['product_cate'] = 1;
        $productinfo['bonus_model_id'] = 0;
        $productinfo['use_ad'] = 0;
        $productinfo['vip_type_reward'] = 0;
        $productinfo['vip_type_require'] = 0;

        $this->data['productinfo'] = $productinfo;

        /* 已勾選的付款方法 */
        $this->data['pay_selected'] = [];

        /*已勾選的運法*/
        $this->data['shipping_selected'] = [];

        return view('admin.productinfo.item-product-info', ['data' => $this->data]);
    }
    /* 編輯 */
    public function edit(Request $request)
    {
        $id = $request->get('id');

        if (!parent::check_controll($this->DBTextConnecter->getTableName(), $id)) {
            $this->error(Lang::get('您無法編輯此項目'));
        }
        /* 指派資料 */
        $this->assign_data();

        $productinfo = DB::table($this->resTableName)->find($id);
        $productinfo = CommonService::objectToArray($productinfo);
        if ($productinfo == null) {
            $this->error(Lang::get('資料有誤'));
        }

        // 預設分類型態為 0（商城）
        if (!isset($productinfo['category_type'])) {
            $productinfo['category_type'] = 0;
        }

        $expiring_product = DB::table('expiring_product')->select('product_id')->whereRaw('product_id <> 0')->get();
        $expiring_product = CommonService::objectToArray($expiring_product);
        $hot_product = DB::table('hot_product')->select('product_id')->whereRaw('product_id <> 0')->get();
        $hot_product = CommonService::objectToArray($hot_product);
        $recommend_product = DB::table('recommend_product')->select('product_id')->whereRaw('product_id <> 0')->get();
        $recommend_product = CommonService::objectToArray($recommend_product);
        $spe_price_product = DB::table('spe_price_product')->select('product_id')->whereRaw('product_id <> 0')->get();
        $spe_price_product = CommonService::objectToArray($spe_price_product);

        $items = self::get_product_types($id);
        $items = CommonService::objectToArray($items);
        $productinfo['items'] = str_replace("\\t", "", json_encode($items));

        $productinfo['expiring_product'] = in_array(
            ['product_id' => $productinfo['id']],
            $expiring_product
        ) ? 1 : 0;

        $productinfo['hot_product'] = in_array(
            ['product_id' => $productinfo['id']],
            $hot_product
        ) ? 1 : 0;

        $productinfo['recommend_product'] = in_array(
            ['product_id' => $productinfo['id']],
            $recommend_product
        ) ? 1 : 0;

        $productinfo['spe_price_product'] = in_array(
            ['product_id' => $productinfo['id']],
            $spe_price_product
        ) ? 1 : 0;

        $productinfo['pic'] = json_decode($productinfo['pic'], true);

        $kol_productinfo = DB::table('kol_productinfo')->whereRaw('productinfo_id =' . $productinfo['id'] . ' AND is_using=1')->orderByRaw('id desc')->get();
        $kol_productinfo = CommonService::objectToArray($kol_productinfo);
        $productinfo['kol_id'] = ($kol_productinfo) ? $kol_productinfo[0]['kol_id'] : 0;

        $this->data['productinfo'] = $productinfo;

        /* 已勾選的付款方法 */
        $pay_where = $productinfo['pay_type'] ? 'id in (' . $productinfo['pay_type'] . ')' : '0 = 1';
        $pay_selected = DB::table('pay_fee')->whereRaw($pay_where)->orderByRaw('order_id asc, id desc')->get();
        foreach ($pay_selected as $key => $value) {
            $pay_selected[$key]->name = str_replace('\\', '\\\\', $value->name);
        }
        $this->data['pay_selected'] = $pay_selected;

        /*已勾選的運法*/
        $shipping_where = $productinfo['shipping_type'] ? 'id in (' . $productinfo['shipping_type'] . ')' : '0 = 1';
        $shipping_selected = DB::table('shipping_fee')->whereRaw($shipping_where)->orderByRaw('order_id asc, id desc')->get();
        $this->data['shipping_selected'] = $shipping_selected;

        //   $this->data['discount_json'] = json_encode($this->data['discount']);
        //   $this->data['pay_fee_json'] = json_encode($this->data['pay_fee']);
        //   $this->data['shipping_fee_json'] = json_encode($this->data['shipping_fee']);
        return view('admin.productinfo.item-product-info', ['data' => $this->data]);
    }

    /* 引資料入html(allcreate,edit使用) */
    private function assign_data()
    {
        /*回饋模組 */
        $bonus_models = BonusSettingHelper::get_bonus_models([]);
        $this->data['bonus_models'] = $bonus_models['db_data'];

        // VIP等級
        $vip_type = MemberInstance::get_vip_types()['db_data'];
        $this->data['vip_types'] =  CommonService::objectToArray($vip_type);

        /* 品項下拉選 */
        $discount = DB::table('discount')->orderByRaw('number asc')->whereRaw($this->distributor_id_where_sql)->get();
        $this->data['discount'] = CommonService::objectToArray($discount);

        /* 庫存區下拉選 */
        $position = DB::table('position')->whereRaw($this->distributor_id_where_sql)->orderByRaw('name asc, id desc')->get();
        $this->data['position'] = CommonService::objectToArray($position);

        /* 分館(自己的) */
        $product = DB::table('product')->select('id', 'title')->whereRaw($this->distributor_id_where_sql)->orderByRaw('order_id asc, id desc')->get();
        $this->data['product'] = CommonService::objectToArray($product);

        /* 分館(平台的) */
        $product_share = DB::table('product')->select('id', 'title')->whereRaw('distributor_id=0')->orderByRaw('order_id asc, id desc')->get();
        $this->data['product_share'] = CommonService::objectToArray($product_share);

        /* 商品描述下拉選 */
        $prodesc = DB::table('prodesc')->whereRaw($this->distributor_id_where_sql)->get();
        $this->data['prodesc'] = CommonService::objectToArray($prodesc);

        /* 網紅 */
        if ($this->admin_type == 'distribution') {
            $kol = [];
        } else {
            $kol = DB::table('kol')->orderByRaw('id desc')->get();
            $kol = CommonService::objectToArray($kol);
        }
        $this->data['kol'] = $kol;

        /* 各種付款方法 */
        $pay_fee = DB::table('pay_fee')->whereRaw('sys_status=1')->orderByRaw('order_id asc, id desc')->get();
        $pay_fee = CommonService::objectToArray($pay_fee);
        foreach ($pay_fee as $key => $value) {
            $pay_fee[$key]['name'] = str_replace('\\', '\\\\', $value['name']);
        }
        $this->data['pay_fee'] = $pay_fee;

        /* 各種運法 */
        $shipping_fee = DB::table('shipping_fee')->whereRaw('sys_status=1')->orderByRaw('order_id asc, id desc')->get();
        $this->data['shipping_fee'] = CommonService::objectToArray($shipping_fee);

        /* 運費標籤 */
        $shipping_fee_tag = DB::table('shipping_fee_tag')->whereRaw($this->distributor_id_where_sql)->orderByRaw('order_id asc, id desc')->get();
        $this->data['shipping_fee_tag'] = CommonService::objectToArray($shipping_fee_tag);
    }

    /*取得商品品項 並組合成編輯時的items*/
    public static function get_product_types($id)
    {
        $productinfo = DB::table('productinfo')->find($id);
        $productinfo = CommonService::objectToArray($productinfo);

        $item = DB::table('productinfo_type as pt')
            ->whereRaw('pt.product_id = ' . $productinfo['id'] . ' AND online=1')
            ->select('pt.*', 'pt.num as online_num', 'pt.position as position_id')
            ->orderByRaw('pt.order_id asc, pt.id asc')->get();
        $item = CommonService::objectToArray($item);
        // dd($item);
        foreach ($item as $k => $v) {
            $item[$k]['title_name'] = $item[$k]['title'];
            $item[$k]['title'] = $item[$k]['discount_id'];
            $item[$k]['position'] = $item[$k]['position_id'];

            if ($productinfo['r_repeat'] == 0) { // 依實體編碼
                $item[$k]['num'] = DB::table('position_portion')->whereRaw("product_id = '" . $v['product_id'] . "' and productinfo_type = '" . $v['id'] . "' and position_id = '" . $v['position_id'] . "' and num!=0")->count();
            } else { // 依品項編碼
                $position_portion = DB::table('position_portion')->whereRaw("product_id = '" . $v['product_id'] . "' and productinfo_type = '" . $v['id'] . "' and position_id = '" . $v['position_id'] . "' and num!=0")->first();
                $position_portion = CommonService::objectToArray($position_portion);
                $item[$k]['num'] = $position_portion ? $position_portion['num'] : 0;
            }

            $item[$k]['old_num'] = $item[$k]['num'];

            unset($item[$k]['position_id']);
            unset($item[$k]['Pp_num']);
        }
        // dd($item);

        return $item;
    }

    /* 新增/編輯商品 */
    public function update(Request $request)
    {
        $newData = $request->post();
        $newData['id'] = isset($newData['id']) ? $newData['id'] : '0';

        // 驗證分類型態 0=商城 1=課程
        $newData['category_type'] = isset($newData['category_type']) ? (int)$newData['category_type'] : 0;

        /*供應商*/
        if ($this->admin_type == 'distribution') {
            $newData['distributor_id'] = session()->get($this->admin_type)['id'];
        } else {
            $distributor_id = $newData['distributor_id'] ?? 0;
            $newData['distributor_id'] = $distributor_id;
            if ($distributor_id != 0) {
                $user_collection = Db::connection('main_db')->table('account')->where('id', $distributor_id)->get();
                $db_id_to_user = $user_collection->keyBy('id')->toArray();
                if (!isset($db_id_to_user[$distributor_id])) {
                    $this->error('供應商ID有誤，無此會員');
                } else if ($db_id_to_user[$distributor_id]->user_type != 1) {
                    $this->error('供應商ID有誤，此會員並非供應商');
                }
            }
        }
        // dd($newData);

        unset($newData['prodesc_select']);
        unset($newData['_token']);

        if ($newData['title'] == '') {
            $this->error(Lang::get('請輸入標題'));
        }
        if ($newData['form_error'] == 1) {
            $this->error(Lang::get('商品位置錯誤'));
        }
        unset($newData['form_error']);

        if ($newData['final_array'] == "{}") {
            $this->error(Lang::get('請給階層位置'));
        }

        // 紀錄圖片資料
        $img_pust = [];
        for ($i = 0; $i < config('control.control_img_quantity'); $i++) {
            $img_pust['image' . $i] = $newData['image' . $i] ?? "";
            $img_pust['delimg' . $i] = $newData['delimg' . $i] ?? "";
            $img_pust['image_base64_' . $i] = $newData['image_base64_' . $i] ?? "";
            unset($newData['image' . $i]);
            unset($newData['delimg' . $i]);
            unset($newData['image_base64_' . $i]);
        }
        if (isset($newData['delimg' . config('control.control_img_quantity')])) {
            $this->error(Lang::get('超出圖片上傳上限'));
        }

        // 記錄網紅id
        $kol_id = isset($newData['kol_id']) ? $newData['kol_id'] : 0;
        unset($newData['kol_id']);

        // 記錄品項資料
        $subProjectJson = $newData['subProjectJson'];
        unset($newData['subProjectJson']);
        $delProjectJson = isset($newData['delProjectJson']) ? $newData['delProjectJson'] : '[]';
        unset($newData['delProjectJson']);
        $index_ADV_Json = $newData['index_ADV_Json'];
        unset($newData['index_ADV_Json']);

        // dump($newData);exit;
        if ($newData['id'] == '0') { // 新增
            if (DB::table('productinfo')->where('ISBN', $newData['ISBN'] ?? '')->whereNotNull('ISBN')->first()) {
                $this->error(Lang::get('商品條碼重複'));
            }

            $newData['create_time'] = time();

            // 產生商品編號
            $newData['product_id'] = config('extra.shop.subDeparment') . 'P' . date('Ymd') . $this->getNumberCount();

            if (empty($newData['keywords']) == true) {
                $newData['keywords'] = '';
            }
            $this->DBTextConnecter->setDataArray($newData);
            $id = $this->DBTextConnecter->createTextRow();
            $newData['id'] = $id;

            /*設定預設欄位*/
            $this->set_default_productinfo_register_fields($id);
        } else { // 編輯
            if (DB::table('productinfo')->where('ISBN', $newData['ISBN'] ?? '')->where('id', '!=', $newData['id'])->whereNotNull('ISBN')->first()) {
                $this->error(Lang::get('商品條碼重複'));
            }
            if (!parent::check_controll($this->DBTextConnecter->getTableName(), $newData['id'])) {
                $this->error(Lang::get('您無法編輯此項目'));
            }
            $this->DBTextConnecter->setDataArray($newData);
            $this->DBTextConnecter->upTextRow();
        }

        // 處理圖片
        $imgData = DB::table('productinfo')->select('pic')->find($newData['id']);
        $imgData = CommonService::objectToArray($imgData);

        // 原先上傳的照片
        if (empty($imgData) == true) {
            $imgData = [];
        } else {
            if (empty($imgData['pic']) == true) {
                $imgData = [];
            } else {
                $imgData = json_decode($imgData['pic'], true);
            }
        }

        $width = 600;
        $height = 600;
        for ($i = 0; $i < config('control.control_img_quantity'); $i++) {
            $img_upload_name = 'productinfo_pic' . $newData['id'] . '_' . $i . '_' . CommonService::randomkeys(3);

            /*以input text上傳的檔案*/
            $image_base64 = isset($img_pust['image_base64_' . $i]) ? $img_pust['image_base64_' . $i] : "";
            // dump($image_base64);
            if ($image_base64) {
                $path_to_file = CommonService::uploadFile('/public/static/index/upload', $image_base64, $img_upload_name);
                $path_to_file = explode("/", $path_to_file);
                $imgData[$i] = '/upload/' . end($path_to_file);
            }

            /*以input file上傳的檔案*/
            $image = $request->file('image' . $i);
            // dump($image);
            if ($image) {
                $imgData[$i] = $this->DBFileConnecter->fixedFileUp($image, $img_upload_name);
            }

            /*刪除的檔案*/
            if (isset($img_pust['delimg' . $i]) == 1) {
                if ($img_pust['delimg' . $i] == 1) {
                    unset($imgData[$i]);
                }
            }
        }
        $imgData = is_array($imgData) ? array_values($imgData) : [];
        $updateData['pic'] = json_encode($imgData);
        $updateData['id'] = $newData['id'];
        $this->DBTextConnecter->setDataArray($updateData);
        $this->DBTextConnecter->upTextRow();

        // 更新網紅
        $this->create_kol_productinfo($newData['id'], $kol_id);

        // 處理排序，建立每個階層與商品的紀錄(用於儲存不同排序)
        $final_array = json_decode($newData['final_array'], true);
        $this->create_productinfo_orders($newData['id'], $final_array);

        // 自動更新排序
        if (isset($newData['order_id'])) {
            $table = $this->resTableName;
            $column = 'order_id';
            $order_num = $newData['order_id'];
            $primary_key = 'id';
            $primary_value = $newData['id'];
            parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
            unset($newData['order_id']);
        }
        $client = new Client(['verify' => false]);

        // 處理品項
        $deal_datetime = $this->deal_with_productinfo_type($request, $newData['id'], $newData['r_repeat'], $subProjectJson, $delProjectJson);
        // 處理標籤(店長、推薦、即期、特價)
        if ($this->admin_type == 'admin') {
            $index_ADV_Json = json_decode($index_ADV_Json);
            foreach ($index_ADV_Json as $value) {
                $Data = [
                    'form_params' => [
                        'tableName' => $value->tableName,
                        'id' => $newData['id']
                    ]
                ];

                $result = ['code' => 1, 'msg' => 1];
                $r_obj = new Request([], [
                    'id' => $newData['id'],
                    'tableName' => $value->tableName,
                ]);
                if ($value->value == 0) {
                    $result = $this->remove_to_index_adv($r_obj);
                } else {
                    $result = $this->add_to_index_adv($r_obj);
                }
                if ($result['code'] == 0) {
                    $this->error($result['msg']);
                }
            }
        }

        if (!empty(config('control.close_function_current')['存放位置管理'])) {
            $this->success(Lang::get('操作成功'), url('admin/Productinfo/edit') . '?id=' . $newData['id']);
        }

        if ($deal_datetime == '') {
            $this->success(Lang::get('操作成功'), url('admin/Productinfo/edit') . '?id=' . $newData['id']);
        } else {
            $this->success(Lang::get('操作成功'), url('/admin/Productinfo/show_position_portion') . '?' . http_build_query(['searchKey' => $newData['id'], 'datetime' => $deal_datetime]));
        }
    }

    /* 建立網紅跟商品關係 */
    private function create_kol_productinfo($prod_id, $kol_id)
    {
        if ($this->admin_type == 'distribution') {
            return;
        }

        $kol_record = DB::table('kol_productinfo')->whereRaw('productinfo_id =' . $prod_id)->orderByRaw('id desc')->get(); //抓取此商品的網紅設定紀錄
        $kol_record = CommonService::objectToArray($kol_record);
        if (!empty($kol_record)) { /*有紀錄*/
            if ($kol_id == $kol_record[0]['kol_id']) { /*修改與紀錄一樣*/
                return; /*不處理*/
            }
        } else { /*無紀錄*/
            if ($kol_id == 0) { /*修改為0*/
                return; /*不處理*/
            }
        }

        $time = time();

        // 修改過去紀錄成過期
        DB::table('kol_productinfo')->where('productinfo_id', $prod_id)->update(['is_using' => 0]);

        DB::table('kol_productinfo')->whereRaw('time_e is null AND productinfo_id =' . $prod_id)->update(['time_e' => $time]);

        // 新增新紀錄
        $newData = [
            'kol_id'        => $kol_id,
            'productinfo_id' => $prod_id,
            'time'            => $time,
            'is_using'        => 1,
        ];
        $DBTextConnecter = DBTextConnecter::withTableName('kol_productinfo');
        $DBTextConnecter->setDataArray($newData);
        $DBTextConnecter->createTextRow($newData);
    }
    /* 建立商品與分館/分類的紀錄(排序用) */
    private function create_productinfo_orders($prod_id, $final_array)
    {
        $id_in = [];
        // 針對每個階層處理
        foreach ($final_array as $key => $value) {
            $productinfo_orders = DB::table('productinfo_orders')->whereRaw('prod_id =' . $prod_id . ' AND prev_id =' . $value['prev_id'] . ' AND branch_id =' . $value['branch_id'])->get();
            $productinfo_orders = CommonService::objectToArray($productinfo_orders);
            if (count($productinfo_orders) > 0) {
                // 有紀錄則紀錄有此筆紀錄
                array_push($id_in, $productinfo_orders[0]['id']);
            } else {
                // 沒紀錄則新增紀錄
                DB::table('productinfo_orders')->insert([
                    'prod_id' => $prod_id,
                    'prev_id' => $value['prev_id'],
                    'branch_id' => $value['branch_id'],
                    'order_id' => 0,
                ]);
                // 記錄剛建立出的資料
                $productinfo_orders = DB::table('productinfo_orders')->whereRaw('prod_id =' . $prod_id . ' AND prev_id =' . $value['prev_id'] . ' AND branch_id =' . $value['branch_id'])->get();
                $productinfo_orders = CommonService::objectToArray($productinfo_orders);
                array_push($id_in, $productinfo_orders[0]['id']);
            }
        }

        // 把沒有的階層刪掉
        if (count($id_in) > 0) {
            $id_in = join(',', $id_in);
            DB::table('productinfo_orders')->whereRaw('prod_id =' . $prod_id . ' AND id not in (' . $id_in . ')')->delete();
        }
    }
    /* 處理商品的品項 */
    public function deal_with_productinfo_type($request, $prod_id, $repeat, $subProjectJson, $delProjectJson)
    {
        $post = $request->post();
        // 檢查編碼是否足夠
        $post_obj = new Request([
            'data' => $subProjectJson,
            'repeat' => $repeat,
        ]);
        $check_position_portion = $this->position_portion($post_obj);
        if ($check_position_portion['form_error'] == 1) $this->error($check_position_portion['alert']);

        $DB_productinfo_type = DBTextConnecter::withTableName('productinfo_type');

        // 處理刪除的品項
        $delProjectJson = json_decode($delProjectJson);
        // dump($delProjectJson);exit;
        foreach ($delProjectJson as $value) {
            DB::table('productinfo_type')->whereRaw('id=' . $value)->update(['num' => 0, 'online' => 0]); /*修改品項線上可購買數量、狀態*/
            DB::table('position_portion')->where('productinfo_type', $value)->delete();/*刪除品項庫存編碼*/
        }

        // 留存的品項的品項
        $subProjectJson = json_decode($subProjectJson);
        // dump($subProjectJson);exit;
        $deal_datetime = date('Y-m-d H:i:s'); // 紀錄操作時間
        $add_position_portion = false;
        foreach ($subProjectJson as $value) {
            $old_poistion = '';
            $value = get_object_vars($value);

            /*處理品項變換數量*/
            if (!empty(config('control.close_function_current')['存放位置管理'])) {
                $change_num = isset($value['online_num']) ? $value['online_num'] : $value['num'];
            } else {
                $change_num = isset($value['old_num']) ? (int)$value['num'] - (int)$value['old_num'] : (int)$value['num']; // 變化量
                if ($change_num < 0) {
                    $this->error(Lang::get('如欲扣除品項數量，請從實體銷存處理'));
                }
            }
            $value['title'] = isset($value['title_name']) ? $value['title_name'] : '';

            unset($value['title_name']);
            unset($value['total']);
            unset($value['old_num']);
            unset($value['online_num']);

            if (array_key_exists('id', $value)) { // 編輯品項
                $productinfo_type = DB::table('productinfo_type')
                    ->select('id', 'num', 'position')
                    ->find($value['id']);
                $productinfo_type = CommonService::objectToArray($productinfo_type);

                $productinfo_type_id = $productinfo_type['id'];
                $old_poistion = $productinfo_type['position']; // 紀錄原始位置
                // 如果關閉了存放位置管理，則使用$change_num作為最終數量，若有開啟則需加上原本的數量
                $value['num'] = !empty(config('control.close_function_current')['存放位置管理']) ? $change_num : $change_num + (int)$productinfo_type['num']; // 線上可購買數量

                $DB_productinfo_type->setDataArray($value);
                $DB_productinfo_type->upTextRow($value);
            } else { // 新增品項
                $value['product_id'] = $prod_id; // 設定商品id
                $value['num'] = $change_num;  // 線上可購買數量
                $DB_productinfo_type->setDataArray($value);
                $productinfo_type_id = $DB_productinfo_type->createTextRow($value);
            }


            if ($old_poistion != $value['position'] && $old_poistion != '') { /*庫存位置不同 且 有原始位置*/
                if (isset($value['id'])) { // 如果有品項id
                    $ori_position_portion = DB::table('position_portion')->where('productinfo_type', $value['id'])->get();
                    $ori_position_portion = CommonService::objectToArray($ori_position_portion);
                    foreach ($ori_position_portion as $k_oripp => $v_oripp) {
                        $position_number = $this->get_position_number($value['position']);
                        DB::table('position_portion')->where('id', $v_oripp['id'])->update([
                            'position_id'        => $value['position'],
                            'position_number'     => $position_number,
                        ]);
                    }
                }
            }

            // 變化量大於0 且 有開啟存放位置管理，則需處理額外庫存編碼數量
            if ($change_num > 0 && empty(config('control.close_function_current')['存放位置管理'])) {
                if (isset($value['id'])) { // 如果有品項id
                    $has_position_portion = DB::table('position_portion')->whereRaw("productinfo_type = '" . $value['id'] . "' and product_id ='" . $value['product_id'] . "'")->get();
                } else {
                    $has_position_portion = false;
                }

                if ($repeat == 1 && $has_position_portion) { // 編碼方式為依品項編碼 且 已經存在編碼
                    DB::table('position_portion')
                        ->whereRaw("productinfo_type = '" . $value['id'] . "' and product_id ='" . $value['product_id'] . "'")
                        ->increment('num', $change_num);
                } else {
                    if ($repeat == 1) { // 編碼方式為依品項編碼 但 不存在編碼
                        // 根據數量指派位置
                        $position_number = $this->get_position_number($value['position']);
                        $newData = [
                            'position_id'       => $value['position'],
                            'position_number'   => $position_number,
                            'num'               => $change_num,
                            'product_id'        => $prod_id,
                            'productinfo_type'  => $productinfo_type_id,
                            'radio'             => $repeat,
                            'datetime'          => $deal_datetime,
                        ];

                        if ($position_number != 0) {
                            $add_position_portion = true;
                            DB::table('position_portion')->insert($newData);
                        } else { // 編碼不足
                            DB::table('productinfo_type')->where('id', $value['id'])->decrement('num', $change_num); // 扣回線上可購買數量
                            $this->error(Lang::get('庫存編碼已達上限，請更換庫存區') . '：' . $value['title']);
                        }
                    } else { // 編碼方式為依實體編碼
                        foreach (range(0, $change_num - 1) as $index) { // 依據新增數量逐個建立編碼
                            // 根據數量指派位置
                            $position_number = $this->get_position_number($value['position']);
                            $newData = [
                                'position_id'         => $value['position'],
                                'position_number'    => $position_number,
                                'num'                => 1,
                                'product_id'        => $prod_id,
                                'productinfo_type'    => $productinfo_type_id,
                                'radio'                => $repeat,
                                'datetime'            => $deal_datetime,
                            ];

                            if ($position_number != 0) {
                                $add_position_portion = true;
                                DB::table('position_portion')->insert($newData);
                            } else {
                                DB::table('productinfo_type')->where('id', $value['id'])->decrement('num', $change_num - $index); // 扣回線上可購買數量
                                $this->error(Lang::get('庫存編碼已達上限，請更換庫存區') . '：' . $value['title']);
                            }
                        }
                    }
                }
            }
        }

        /*更新訂單標記的實體庫存狀態(檢查「超額購買」的訂單是否「足夠」)*/
        OrderHelper::check_stock();

        if ($add_position_portion) { // 有新增position_portion
            return $deal_datetime; // 回傳處理時間
        } else {
            return '';
        }
    }
    /* 取得下一個庫存編碼 */
    private function get_position_number($position_id)
    {
        $position = DB::table('position')->where('id', $position_id)->first();
        $position = CommonService::objectToArray($position);
        $position_number = 1;

        if ($position['max'] == 1) { // 無限
            while (true) {
                $has_empty_position = DB::table('position_portion')->whereRaw('position_id=' . $position_id . ' AND position_number=' . $position_number)->get();
                $has_empty_position = CommonService::objectToArray($has_empty_position);
                if ($has_empty_position) {
                    $position_number += 1; // +1後繼續找
                } else {
                    break;
                }
            }
        } else {
            while ($position_number <= $position['number']) {
                $has_empty_position = DB::table('position_portion')->whereRaw('position_id=' . $position_id . ' AND position_number=' . $position_number)->get();
                $has_empty_position = CommonService::objectToArray($has_empty_position);
                if ($has_empty_position) {
                    $position_number += 1;
                } else {
                    break;
                }
            }

            $position_number = $position_number <= $position['number'] ? $position_number : 0;
        }

        return $position_number;
    }

    /*AJAX 編輯商品*/
    public function cellCtrl(Request $request)
    {
        // try{
        $updateData = $request->post();
        if (!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])) {
            throw new \Exception(Lang::get('您無法編輯此項目'));
        }
        if (isset($updateData['po_order_id'])) { /*改分館類 排序*/
            // 自動更新排序
            $table = 'productinfo_orders';
            $column = 'order_id';
            $order_num = $updateData['po_order_id'];
            $primary_key = 'id';
            $productinfo_orders_where = 'prod_id =' . $updateData['id'] . ' AND branch_id =' . $updateData['branch_id'];
            if ($updateData['prev_id'] != 0) {
                $productinfo_orders_where .= ' AND prev_id =' . $updateData['prev_id'];
            }
            $primary_value = DB::table('productinfo_orders')->whereRaw($productinfo_orders_where)->first()->id;
            $filter_where = 'prev_id =' . $updateData['prev_id'] . ' AND branch_id =' . $updateData['branch_id'];
            parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
            unset($updateData['po_order_id']);
        } else if (isset($updateData['po_top_order_id'])) { /*改分館類 置頂排序*/
            if ($this->admin_type == 'distribution') {
                throw new \Exception(Lang::get('您無法編輯此項目'));
            }
            // 自動更新排序
            $table = 'productinfo_orders';
            $column = 'top_order_id';
            $order_num = $updateData['po_top_order_id'];
            $primary_key = 'id';
            $productinfo_orders_where = 'prod_id =' . $updateData['id'] . ' AND branch_id =' . $updateData['branch_id'];
            if ($updateData['prev_id'] != 0) {
                $productinfo_orders_where .= ' AND prev_id =' . $updateData['prev_id'];
            }
            $primary_value = DB::table('productinfo_orders')->whereRaw($productinfo_orders_where)->select('id')->first()->id;
            $filter_where = 'prev_id =' . $updateData['prev_id'] . ' AND branch_id =' . $updateData['branch_id'];
            parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
            unset($updateData['po_top_order_id']);
        } else {/*修改的是商品資料*/
            if (isset($updateData['order_id'])) { /*改排序*/
                // 自動更新排序
                $table = $this->resTableName;
                $column = 'order_id';
                $order_num = $updateData['order_id'];
                $primary_key = 'id';
                $primary_value = $updateData['id'];
                parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
                unset($updateData['order_id']);
            } else if (isset($updateData['top_order_id'])) { /*改置頂排序*/
                if ($this->admin_type == 'distribution') {
                    throw new \Exception(Lang::get('您無法編輯此項目'));
                }
                // 自動更新排序
                $table = $this->resTableName;
                $column = 'top_order_id';
                $order_num = $updateData['top_order_id'];
                $primary_key = 'id';
                $primary_value = $updateData['id'];
                parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
                unset($updateData['top_order_id']);
            }
            $this->DBTextConnecter->setDataArray($updateData);
            $this->DBTextConnecter->upTextRow();
        }
        // }catch (\Exception $e){
        //   $this->error($e->getMessage());
        // }
        $this->success(Lang::get('操作成功'));
    }
    /*AJAX 刪除單一商品*/
    public function delete(Request $request)
    {
        $id = $request->get('id');
        if (!parent::check_controll($this->DBTextConnecter->getTableName(), $id)) {
            $this->error(Lang::get('您無法編輯此項目'));
        }
        try {
            DB::table('productinfo')->delete($id);
            DB::table('expiring_product')->where('product_id', $id)->update(['product_id' => 0]);
            DB::table('hot_product')->where('product_id', $id)->update(['product_id' => 0]);
            DB::table('recommend_product')->where('product_id', $id)->update(['product_id' => 0]);
            DB::table('spe_price_product')->where('product_id', $id)->update(['product_id' => 0]);
            DB::table('productinfo_type')->where('product_id', $id)->delete();
            DB::table('position_portion')->where('product_id', $id)->delete();
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success(Lang::get('操作成功'), url('All/index'));
    }
    /*AJAX 刪除多個商品*/
    public function multiDelete(Request $request)
    {
        $idList = $request->post('id');
        try {
            if ($idList) {
                $idList = json_decode($idList);
                foreach ($idList as $id) {
                    if (!parent::check_controll($this->DBTextConnecter->getTableName(), $id)) {
                        throw new \Exception(Lang::get('您無法編輯此項目'));
                    }
                }
                DB::table('productinfo')->whereIn('id', $idList)->delete();
                DB::table('expiring_product')->where('product_id', 'in', $idList)->update(['product_id' => 0]);
                DB::table('hot_product')->where('product_id', 'in', $idList)->update(['product_id' => 0]);
                DB::table('recommend_product')->where('product_id', 'in', $idList)->update(['product_id' => 0]);
                DB::table('spe_price_product')->where('product_id', 'in', $idList)->update(['product_id' => 0]);
                DB::table('productinfo_type')->where('product_id', 'in', $idList)->delete();
                DB::table('position_portion')->where('product_id', 'in', $idList)->delete();
            }
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success(Lang::get('操作成功'));
    }

    /* 複製商品 */
    public function copy(Request $request)
    {
        $idList = $request->get('id');
        if (!$idList) {
            throw new \Exception(Lang::get('資料有誤'));
        }
        $idList = json_decode($idList);
        // dump($idList);exit;

        array_walk($idList, function ($value) {
            $productinfo = DB::table('productinfo')->find($value);
            $productinfo = CommonService::objectToArray($productinfo);
            $old_id = $productinfo['id'];
            unset($productinfo['id']);
            unset($productinfo['ISBN']);
            unset($productinfo['updatetime']);

            if ($this->admin_type == 'distribution') {
                if ($productinfo['distributor_id'] != session()->get($this->admin_type)['id']) {
                    return;
                }
            } else {
                unset($productinfo['distributor_id']);
            }

            // 產生商品編號
            $productinfo['product_id'] = config('extra.shop.subDeparment') . 'P' . date('Ymd') . $this->getNumberCount();

            $id = DB::table('productinfo')->insertGetId($productinfo);

            /*設定預設欄位*/
            $this->set_default_productinfo_register_fields($id);

            // 複製圖片
            $productinfo["pic"] = json_decode($productinfo["pic"], true);
            foreach ($productinfo["pic"] as $k => $v) {
                $str_sec = explode("?", $productinfo["pic"][$k]);
                $oldPath = $str_sec[0];
                $newPath = str_replace($old_id, $id, $str_sec[0]);
                copy(ROOT_PATH . 'public/static/index' . $oldPath, ROOT_PATH . 'public/static/index' . $newPath);
                $productinfo["pic"][$k] = str_replace($old_id, $id, $productinfo["pic"][$k]);
            }

            $productinfo["pic"] = json_encode($productinfo["pic"]);
            DB::table('productinfo')->where('id', $id)->update(['pic' => $productinfo["pic"]]);
        });

        $this->success(Lang::get('操作成功'));
    }

    /* 修改商品建立計數(用於產生品號) */
    private function getNumberCount()
    {
        $count = DB::table('productinfo')->whereRaw('product_id like "' . config('extra.shop.subDeparment') . 'P' . date('Ymd') . '%"')->orderByRaw('id desc')->first();
        $count = CommonService::objectToArray($count);
        $count = $count ? intval(substr($count['product_id'], -3)) + 1 : 1;

        if ($count < 10) {
            $count = '00' . $count;
        } else if ($count < 100) {
            $count = '0' . $count;
        }

        return $count;
    }

    /* 處理標籤勾選 */
    public function add_to_index_adv(Request $request)
    {
        if ($this->admin_type == 'distribution') {
            return ['code' => 0, 'msg' => Lang::get('您無法操作此項目'),];
        }

        $id = $privateId ?? $request->post('id');
        $tableName = $privateTableName ?? $request->post('tableName');

        //檢查是否有勾選過
        $check_exit = DB::table($tableName)->where('product_id', $id)->get();
        if (count($check_exit) > 0) {
            return ['code' => 1, 'msg' => Lang::get('項目已存在'),];
        }

        if ($tableName == 'spe_price_product') {
            $newData = [
                'product_id' => $id,
            ];
            DB::table($tableName)->insert($newData);
            return ['code' => 1, 'msg' => Lang::get('操作成功'),];
        } else {
            $emptyId = DB::table($tableName)->select('id')->where('product_id', 0)->first();
            $emptyId = CommonService::objectToArray($emptyId);
            $emptyId = $emptyId ?? [];
            if (count($emptyId) != 0) {
                $updateData = [
                    'id' => $emptyId['id'],
                    'product_id' => $id,
                ];
                $DBTextConnecter = DBTextConnecter::withTableName($tableName);
                $DBTextConnecter->setDataArray($updateData);
                $DBTextConnecter->upTextRow();
                return ['code' => 1, 'msg' => Lang::get('操作成功'),];
            } else {
                return ['code' => 0, 'msg' => Lang::get('已經滿十個了，請取消一部分商品'),];
            }
        }
    }
    public function remove_to_index_adv(Request $request)
    {
        if ($this->admin_type == 'distribution') {
            return ['code' => 0, 'msg' => Lang::get('您無法操作此項目'),];
        }

        $id = $privateId ?? $request->post('id');
        $tableName = $privateTableName ?? $request->post('tableName');

        if ($tableName == 'spe_price_product') {
            DB::table($tableName)->where('product_id', $id)->delete();
            return ['code' => 1, 'msg' => Lang::get('操作成功'),];
        } else {
            $emptyId = DB::table($tableName)->select('id')->where('product_id', $id)->first();
            $emptyId = CommonService::objectToArray($emptyId);
            $emptyId = $emptyId ?? [];
            if (count($emptyId) != 0) {
                $updateData = [
                    'id' => $emptyId['id'],
                    'product_id' => 0,
                ];
                $DBTextConnecter = DBTextConnecter::withTableName($tableName);
                $DBTextConnecter->setDataArray($updateData);
                $DBTextConnecter->upTextRow();
                return ['code' => 1, 'msg' => Lang::get('操作成功'),];
            } else {
                return ['code' => 1, 'msg' => Lang::get('項目已不存在'),];
            }
        }
    }

    /* 取得分館/分類下選 */
    public function position_select(Request $request)
    { //fat
        $post = $request->post();
        $type = $post['type'] ?? 'product_select'; //product_select
        $po =  $post['next'] ?? '0';
        $value = $post['value'] ?? '0';
        $target_num = $post['target_num'] ?? '';

        switch ($type) {
            case 'product_select':
                $parent_array = DB::table('typeinfo')->whereRaw("parent_id='" . $value . "'  and branch_id='0' ")->select('id', 'title')->orderByRaw('order_id asc, id desc')->get();
                $parent_array = CommonService::objectToArray($parent_array);
                if ($parent_array) {
                    $return_select = '
            <select class="branch_select' . $target_num . '-1" onchange="product_position(\'branch_select\',\'1\',' . $target_num . ')">
            <option value="0">' . Lang::get('請選擇階層') . '</option>
          ';

                    foreach ($parent_array as $v => $k) {
                        $return_select .=
                            '<option value="' . $k['id'] . '">' . $k['title'] . '</option>';
                    }

                    $return_select .= '</select><span class="branch_select' . $target_num . '-2"></span>';
                    echo $return_select;
                }
                break;

            case 'branch_select':
                $parent_array = DB::table('typeinfo')->whereRaw("branch_id='" . $value . "'  and  online = '1'")->select('id', 'title')->orderByRaw('order_id asc, id desc')->get();
                if ($parent_array) {
                    $return_select = '
            <select class="branch_select' . $target_num . '-' . ($po + 1) . '" onchange="product_position(\'branch_select\',' . ($po + 1) . ',' . $target_num . ')">
            <option value="0">' . Lang::get('請選擇階層') . '</option>
          ';

                    if ($value != '0') {
                        foreach ($parent_array as $v => $k) {
                            $return_select .=
                                '<option value="' . $k['id'] . '">' . $k['title'] . '</option>';
                        }
                    }

                    $return_select .= '</select><span class="branch_select' . $target_num . '-' . ($po + 2) . '"></span>';
                    echo $return_select;
                }
                break;
        }
    }

    /* 檢查編碼是否超出上限 */
    public function position_portion(Request $request)
    {
        $data = json_decode($request->post('data'), true);
        $data = $data ?? [];
        // dump($data);exit;

        $pos_num = [];
        $repeat = $request->post('repeat') ?? 0; // 沒repeat值預設為重複

        //處理相同position代碼及數量
        foreach ($data as $k => $v) {
            if ($v['num'] == 0) {
                continue;
            }
            if (!empty(config('control.close_function_current')['存放位置管理'])) {
                continue;
            } /*關閉 存放位置管理功能*/

            if ($v['position'] == 0 && empty(config('control.close_function_current')['存放位置管理'])) { /*未選庫存區 且 使用 存放位置管理功能*/
                $msg['alert'] = Lang::get('請選品項的擇存放位置');
                $msg['form_error'] = 1;
                return $msg;
            }

            if (empty($pos_num[$v['position']])) {
                $pos_num[$v['position']]['new_num'] = 0;
                $pos_num[$v['position']]['old_num'] = $v['old_num'] ?? 0;
            }

            if ($repeat == 0) { //不重複
                $v['old_num'] =  isset($v['old_num']) ? $v['old_num'] : 0;
                $pos_num[$v['position']]['new_num'] += ($v['num'] - $v['old_num']);
            } else { //重複
                if (!isset($v['id'])) {
                    $pos_num[$v['position']]['new_num']++;
                }
            }
        }

        $msg['alert'] = 'ok';
        $msg['form_error'] = 0;
        foreach ($pos_num as $k => $v) {
            $position_portion = DB::table('position_portion')->whereRaw("position_id = '" . $k . "' and product_id != '0' ")->selectRaw('count(position_id) as num')->groupBy('position_id')->get(); //已用位置
            $position_portion = CommonService::objectToArray($position_portion);

            $position = DB::table('position')->select('distributor_id', 'name', 'number', 'max')->where('id', $k)->first(); //位置限制數量
            $position = CommonService::objectToArray($position);
            //dump($position['number']);

            if ($this->admin_type == 'distribution') {
                if ($position['distributor_id'] != session()->get($this->admin_type)['id']) {
                    $msg['alert'] = Lang::get('您無法操作此項目') . '：' . $position['name'];
                    $msg['form_error'] = 1;
                    break;
                }
            }

            if ($position['max'] == '1') {
                continue;
            }

            $use_pos = $position_portion['num'] ?? 0 + $v['new_num'];
            if ($position['number'] < $use_pos) {
                $msg['alert'] = Lang::get('位置不夠') . ':' . $position['name'];
                $msg['form_error'] = 1;
                break;
            }
        }
        //dump($data);
        //dump($pos_num);
        return $msg;
    }

    /* 儲存商品說明/屬性/訂購須知/付款方式 */
    public function cellCtrlFromDefault(Request $request)
    {
        $default_type = $request->post('default_type') ?? 'productinfo';
        try {
            $updateData = $request->post();
            $DBTextConnecter = DBTextConnecter::withTableName('default_content');
            $default_content = DB::table($DBTextConnecter->getTableName())->whereRaw($this->distributor_id_where_sql)->where('default_type', $default_type)->first();
            $default_content = CommonService::objectToArray($default_content);
            if ($default_content) {
                $updateData['id'] = $default_content['id'];
                $DBTextConnecter->setDataArray($updateData);
                $DBTextConnecter->upTextRow();
            } else {
                $updateData['distributor_id'] = session()->get($this->admin_type)['id'];
                $updateData['default_type'] = 'productinfo';
                $DBTextConnecter->setDataArray($updateData);
                $DBTextConnecter->createTextRow();
            }
            $outputData = [
                'status' => true,
                'message' => 'success'
            ];
        } catch (\Exception $e) {
            $outputData = [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
        return $outputData;
    }

    /* 載入商品說明/屬性/訂購須知/付款方式 */
    public function cellGetFromDefault(Request $request)
    {
        $default_type = $request->post('default_type') ?? 'productinfo';
        try {
            $default_content = DB::table('default_content')->whereRaw($this->distributor_id_where_sql)->where('default_type', $default_type)->first();
            $default_content = CommonService::objectToArray($default_content);
            if ($default_content) {
                $message = $default_content[$request->post('textNumber')];
                $message = $message ? $message : "";
            } else {
                $message = "";
            }
            $outputData = [
                'status' => true,
                'message' => $message,
            ];
        } catch (\Exception $e) {
            $outputData = [
                'status' => false,
                'message' => $e->getMessage(),
            ];
        }
        return $outputData;
    }

    /*生成商品基本檔*/
    public function product_example(Request $request)
    {
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $objPHPExcel->setActiveSheetIndex(0);
        $product_columns = [
            '1' => Lang::get('名稱'),
            '2' => Lang::get('條碼'),
            '3' => Lang::get('商品描述'),
            '4' => Lang::get('成本')
        ];

        foreach ($product_columns as $key => $value) {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($key, 1, $value);
        }

        $PHPExcelWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
        $filename = '當前商品範例檔.xls';
        ob_end_clean();
        ob_start();
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"$filename\"");
        $PHPExcelWriter->save('php://output');
    }

    /*生成品項excel檔*/
    public function price_example(Request $request)
    {
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $objPHPExcel->setActiveSheetIndex(0);
        $price_columns = [
            Lang::get('定價'),
            Lang::get('品項'),
            Lang::get('售價'),
        ];
        if (empty(config('control.close_function_current')['庫存警示'])) {
            array_push($price_columns, Lang::get('庫存數量'));
            array_push($price_columns, Lang::get('警示數量'));
        }
        if (empty(config('control.close_function_current')['存放位置管理'])) {
            array_push($price_columns, Lang::get('存放位置'));
        }
        array_push($price_columns, Lang::get('對應圖片'));
        array_push($price_columns, Lang::get('顯示期限') . '(YYYY-mm-dd~YYYY-mm-dd)');
        if (config('control.control_register')) {
            array_push($price_columns, Lang::get('活動時間') . '(YYYY-mm-dd HH:ii~YYYY-mm-dd HH:ii)');
            array_push($price_columns, Lang::get('活動提醒時間') . '(' . Lang::get('有設定則會於整點寄送提醒信') . ' YYYY-mm-dd HH:ii)');
        }
        array_push($price_columns, Lang::get('排序'));

        foreach ($price_columns as $key => $value) {
            $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        $PHPExcelWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xlsx');
        $filename = Lang::get('品項範例檔') . ".xlsx";
        ob_end_clean();
        ob_start();
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"$filename\"");
        $PHPExcelWriter->save('php://output');
    }


    /*設定報名欄位*/
    /*商品欄位列表*/
    public function edit_fields(Request $request)
    {
        $id = $request->get('id');
        $productinfo = DB::table('productinfo')->where('id', $id)->first();
        $productinfo = CommonService::objectToArray($productinfo);
        if (empty($productinfo) == true) {
            $this->error(Lang::get('資料有誤'));
        }
        if ($this->admin_type == 'distribution') {
            if ($productinfo['distributor_id'] != session()->get($this->admin_type)['id']) {
                $this->error(Lang::get('您無法操作此項目'));
            }
        }
        $this->data['productinfo'] = $productinfo;

        /*設定預設欄位*/
        $this->set_default_productinfo_register_fields($id);

        /*共用的欄位特性*/
        $get_fields_type_par = ExaminationHelper::get_fields_type_par();
        // dump($get_fields_type_par);exit;
        foreach ($get_fields_type_par as $key => $value) {
            $this->data[$key] = $value;
        }

        //   $this->data['types_need_option'] = json_encode($this->data['types_need_option']);
        //   $this->data['types_need_limit'] = json_encode($this->data['types_need_limit']);
        return view('admin.productinfo.edit_fields', ['data' => $this->data]);
    }
    private function set_default_productinfo_register_fields($id)
    {
        /*檢查是否有套用預設欄位了*/
        $pre_set = DB::table('productinfo_register_fields')->where('prod_id', $id)->whereRaw('fields_set_id in (1,2)')->get();
        if (count($pre_set) < 2) {
            DB::table('productinfo_register_fields')->where('prod_id', $id)->whereRaw('fields_set_id in (1,2)')->delete();
            $pre_set_fields = DB::table('fields_set')->whereRaw('id in (1,2)')->orderByRaw('order_id asc, id desc')->get();
            $pre_set_fields = CommonService::objectToArray($pre_set_fields);
            foreach ($pre_set_fields as $key => $value) {
                $pre_set_fields[$key]['fields_set_id'] = $value['id'];
                $pre_set_fields[$key]['prod_id'] = $id;
                $pre_set_fields[$key]['order_id'] = $key;
                unset($pre_set_fields[$key]['id']);
                unset($pre_set_fields[$key]['distributor_id']);
            }
            //dd($pre_set_fields);
            DB::table('productinfo_register_fields')->insert($pre_set_fields);
        }
    }

    /*Api 取得商品欄位資料*/
    public function get_prod_fields(Request $request)
    {
        $id = $request->get('id');
        $productinfo = DB::table('productinfo')->find($id);
        $productinfo = CommonService::objectToArray($productinfo);
        $order_by_order = 'order_id asc, id desc';
        $order_by_title = 'title asc, id asc';

        /*商品的欄位資料*/
        $datas_not_times = DB::table('productinfo_register_fields')->where('prod_id', $id)->whereRaw('type!="checkbox_time"')->orderByRaw($order_by_order)->get();
        $datas_not_times = CommonService::objectToArray($datas_not_times);
        $datas_not_times = array_reverse($datas_not_times);
        $datas = DB::table('productinfo_register_fields')->where('prod_id', $id)->whereRaw('type="checkbox_time"')->orderByRaw($order_by_title)->get();
        $datas = CommonService::objectToArray($datas);
        foreach ($datas_not_times as $key => $value) {
            array_unshift($datas, $value);
        }

        foreach ($datas as $key => $value) {
            if (isset($value['options'])) {
                $datas[$key]['options'] = $value['options'] ? json_decode($value['options']) : [];
            } else {
                $datas[$key]['options'] = [];
            }
            if (isset($value['options_cate'])) {
                $datas[$key]['options_cate'] = $value['options_cate'] ? json_decode($value['options_cate']) : [];
            } else {
                $datas[$key]['options_cate'] = [];
            }
        }
        $return_data['datas'] = $datas;

        /*常用欄位*/
        $fields_set = DB::table('fields_set')
            ->where('distributor_id', $productinfo['distributor_id'])
            ->where('online', 1)
            ->whereRaw('id not in (1,2)')
            ->orderByRaw('order_id asc, id desc')->get();
        $fields_set = CommonService::objectToArray($fields_set);
        foreach ($fields_set as $key => $value) {
            $fields_set[$key]['options'] = $value['options'] ? json_decode($value['options']) : [];
        }
        $return_data['fields_set'] = $fields_set;

        /*常用註記詞*/
        $comments_set = DB::table('comments_set')
            ->where('distributor_id', $productinfo['distributor_id'])
            ->where('online', 1)
            ->orderByRaw('order_id asc, id desc')->get();
        $return_data['comments_set'] = $comments_set;

        return $return_data;
    }
    /*新增、修改*/
    public function fields_set_save(Request $request)
    {
        $post = $request->post();
        $data = [
            'fields_set_id' => isset($post['fields_set_id']) ? $post['fields_set_id'] : 0,
            'prod_id' => isset($post['prod_id']) ? $post['prod_id'] : 0,
            'title' => isset($post['title']) ? $post['title'] : "",
            'type' => isset($post['type']) ? $post['type'] : "",
            'required' => isset($post['required']) ? $post['required'] : 1,
            'special' => isset($post['special']) ? $post['special'] : 1,
            'limit' => isset($post['limit']) ? $post['limit'] : "",
            'discription' => isset($post['discription']) ? $post['discription'] : "",
            'options' => isset($post['options']) ? json_encode($post['options'], JSON_UNESCAPED_UNICODE) : "[]",
            'online' => isset($post['online']) ? $post['online'] : 1,
        ];
        if (isset($post['order_id'])) {
            $data['order_id'] = $post['order_id'] === null ? 0 : $post['order_id'];
        }

        if (!isset($post['id'])) {
            $this->error(Lang::get('資料不完整'));
        }
        $id = $post['id'];

        if ($data['prod_id'] == 0) {
            $this->error(Lang::get('資料有誤'));
        }
        if (!$data['type']) {
            $this->error(Lang::get('請選擇資料類型'));
        }
        if (!$data['title']) {
            $this->error(Lang::get('請輸入標題'));
        }

        $productinfo = DB::table('productinfo')->find($data['prod_id']);
        $productinfo = CommonService::objectToArray($productinfo);
        if ($productinfo == null) {
            $this->error(Lang::get('資料有誤'));
        }
        if ($this->admin_type == 'distribution') {
            if ($productinfo['distributor_id'] != session()->get($this->admin_type)['id']) {
                $this->error(Lang::get('您無法操作此項目'));
            }
        }

        if ($id == '0') { /*新增*/
            $id = DB::table('productinfo_register_fields')->insertGetId($data);
        } else { /*編輯*/
            DB::table('productinfo_register_fields')->where('id', $id)->update($data);
        }
        if (isset($data['order_id'])) {
            // 自動更新排序
            $table = 'productinfo_register_fields';
            $column = 'order_id';
            $order_num = $data['order_id'];
            $primary_key = 'id';
            $primary_value = $id;
            $filter_where = 'prod_id="' . $data['prod_id'] . '"';
            parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
        }

        $this->success(Lang::get('操作成功'));
    }
    /*刪除*/
    public function fields_set_delete(Request $request)
    {
        $post = $request->post();

        if (!isset($post['id'])) {
            $this->error(Lang::get('資料不完整'));
        }
        $target = DB::table('productinfo_register_fields')->where('id', $post['id'])->first();
        $target = CommonService::objectToArray($target);
        if (in_array($target['fields_set_id'], [1, 2])) {
            $this->error(Lang::get('您無法操作此項目'));
        }

        $productinfo_register_fields = DB::table('productinfo_register_fields')->where('id', $post['id'])->first();
        $productinfo_register_fields = CommonService::objectToArray($productinfo_register_fields);
        if (!$productinfo_register_fields) {
            $this->error(Lang::get('資料有誤'));
        }
        $productinfo = DB::table('productinfo')->find($productinfo_register_fields['prod_id']);
        $productinfo = CommonService::objectToArray($productinfo);
        if ($productinfo == null) {
            $this->error(Lang::get('資料有誤'));
        }
        if ($this->admin_type == 'distribution') {
            if ($productinfo['distributor_id'] != session()->get($this->admin_type)['id']) {
                $this->error(Lang::get('您無法操作此項目'));
            }
        }

        DB::table('productinfo_register_fields')->where('id', $post['id'])->delete();
        $this->success(Lang::get('操作成功'));
    }
}
