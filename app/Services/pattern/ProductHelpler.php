<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

use App\Services\CommonService;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;
// use App\Services\pattern\recursiveCorrdination\discountRC\Proposal as DiscountProposal;
// use App\Services\pattern\recursiveCorrdination\discountRC\MemberFactory as DiscountMemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\CouponCheck;

use Illuminate\Support\Facades\Lang;

class ProductHelpler
{
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  static public function search_product($search_data = [], $show_all = false)
  {
    $per_page_rows = $show_all ? 999999999 : self::PER_PAGE_ROWS;

    // 分館分類搜尋
    $searchPrev = $search_data['searchPrev'] ?? 0;
    $searchBranch = $search_data['searchBranch'] ?? 0;
    if ($searchBranch != 0) {
      // 使用分類搜尋
      $searchPrev = DB::table('typeinfo')->whereRaw('id = ' . $searchBranch)->first()->parent_id;
      $cate_serach = '"branch_id":"' . $searchBranch . '"';
      // 依商品在該分類的order_id排序
      $productinfo_orders_where = 'AND po.branch_id=' . $searchBranch;
      $order_sql = "po.top_order_id asc, po.order_id asc, productinfo.id desc";
    } elseif ($searchPrev != 0) {
      // 使用分館搜尋
      $cate_serach = '"prev_id":"' . $searchPrev . '","branch_id":"0","parent_id":"0"';
      // 依商品在該分館的order_id排序
      $productinfo_orders_where = 'AND (po.prev_id=' . $searchPrev . ' AND po.branch_id=0)';
      $order_sql = "po.top_order_id asc, po.order_id asc, productinfo.id desc";
    } else {
      // 不使用分館分類搜尋
      $cate_serach = '';
      // 依 productinfo 本身的order_id 排序
      $productinfo_orders_where = "";
      $order_sql = "productinfo.top_order_id asc, productinfo.order_id asc, productinfo.id desc";
    }
    /*額外排序調整*/
    $sort_method = $search_data['sort_method'] ?? '';
    if ($sort_method == "price_asc") {
      $order_sql = 'productinfo.has_price asc, p_t.count asc, ' . $order_sql;
    } else if ($sort_method == "price_desc") {
      $order_sql = 'productinfo.has_price desc, p_t.count desc, ' . $order_sql;
    } else if ($sort_method == "createtime_asc") {
      $order_sql = 'productinfo.id asc, ' . $order_sql;
    } else if ($sort_method == "createtime_desc") {
      $order_sql = 'productinfo.id desc, ' . $order_sql;
    }
    $search_result['sort_method'] = $sort_method;

    $cate_serach = $cate_serach == '' ? "" : "and final_array like '%" . $cate_serach . "%'";
    $search_result['searchPrev'] = $searchPrev;
    $search_result['searchBranch'] = $searchBranch;

    // 商品名稱、品項、條碼搜尋
    $searchKey = $search_data['searchKey'] ?? '';
    $search_result['searchKey'] = $searchKey;
    if (!empty($searchKey)) {
      $seach_where = "( UPPER(productinfo.title) like  UPPER('%" . $searchKey . "%') OR productinfo.product_id like '%" . $searchKey . "%' OR productinfo.ISBN like '%" . $searchKey . "%' )";
    } else {
      $seach_where = 'true';
    }

    // 庫存編碼搜尋
    $position_id = $search_data['position_id'] ?? '';
    $search_result['position_id'] = $position_id;
    if ($position_id != '') {
      $seach_where .= " AND ( position_portion.position_id = '" . $position_id . "' )";
    }
    $position_number = $search_data['position_number'] ?? '';
    $search_result['position_number'] = $position_number;
    if ($position_number != '') {
      $seach_where .= " AND ( position_portion.position_number =  '" . $position_number . "' )";
    }

    // 標籤勾選搜尋(人氣、店長...)
    $ck = $_GET['ck'] ?? '';
    $ck_where = '';
    if (!empty($_GET['ck'])) {
      foreach ($_GET['ck'] as $k => $v) {
        if ($v != 0) {
          $ck_where .= ' and ' . $k . '.product_id IS NOT NULL ';
        }
      }
    }

    // 網紅搜尋
    $kol_id = $search_data['kol_id'] ?? '-1';
    $search_result['kol_id'] = $kol_id;
    if ($kol_id == -1) { // 不依搜尋網紅
      $kol_search = ' AND ( kp.is_using=1 OR kp.kol_id is null)';
    } else if ($kol_id == 0) { // 搜尋無網紅
      $kol_search = ' AND ( (kp.kol_id =0 AND kp.is_using=1) OR kp.kol_id is null )';
    } else { // 搜尋某網紅的
      $kol_search = ' AND kp.kol_id =' . $kol_id . ' AND kp.is_using=1';
    }

    // 啟用停用搜尋
    $online = $search_data['online'] ?? '-1';
    $search_result['online'] = $online;
    if ($online == -1) { // 不依啟用停用搜尋
      $online_search = '';
    } else { // 搜尋啟用停用
      $online_search = ' AND productinfo.online =' . $online;
    }

    // 價格區間搜尋
    $price_range_search = $search_data['price_range_search'] ?? '';
    if ($price_range_search) {
      $price_range_searchs = explode('~', $price_range_search);
      if (count($price_range_searchs) == 2) {
        if ($price_range_searchs[0]) {
          $seach_where .= " AND p_t.count >=" . $price_range_searchs[0];
        }
        if ($price_range_searchs[1]) {
          $seach_where .= " AND p_t.count <=" . $price_range_searchs[1];
        }
      }
    }
    $search_result['price_range_search'] = $price_range_search;

    $product_cate = $search_data['product_cate'] ?? '';
    if ($product_cate) {
      $seach_where .= " AND productinfo.product_cate =" . $product_cate;
    }
    $bonus_model_id = $search_data['bonus_model_id'] ?? '';
    if ($bonus_model_id !== '') {
      $seach_where .= " AND productinfo.bonus_model_id =" . $bonus_model_id;
    }

    $frontend_view = $search_data['frontend_view'] ?? false;
    if ($frontend_view) { // 前台查看
      $frontend_user_id = $search_data['frontend_user_id'] ?? 0;
      // 依會員的商品瀏覽等級查看
      $where_user_view_product = self::get_user_view_product_limit($frontend_user_id);
      if ($where_user_view_product) {
        $seach_where .= ' AND productinfo.' . $where_user_view_product;
      }
    }

    //依活動(或立馬省)篩選
    $act_id = $search_data['act_id'] ?? '';
    if ($act_id) {
      $in_ids = [];
      $act_product = DB::table('act_product')->where('act_id', $act_id)->get();
      $act_product = CommonService::objectToArray($act_product);
      foreach ($act_product as $key2 => $value2) {
        array_push($in_ids, $value2['prod_id']);
      };
      if (count($in_ids) > 0) {
        $seach_where .= ' AND productinfo.id in (' . implode(',', $in_ids) . ')';
      }
    }

    // dump("{$seach_where} {$ck_where} {$cate_serach} {$kol_search} {$online_search} {$productinfo_orders_where}");exit;
    $target_products_query = DB::table('productinfo')
      ->select(
        'productinfo.title AS title',
        'productinfo.house_date',
        'productinfo.id AS id',
        'productinfo.pic AS pic1',
        'productinfo.has_price AS has_price',
        'productinfo.product_cate',
        'productinfo.is_registrable'
      )
      ->leftJoin('productinfo_type as p_t', 'productinfo.id', 'p_t.product_id')
      ->leftJoin('position_portion', 'productinfo.id', 'position_portion.product_id')
      ->leftJoin('expiring_product', 'productinfo.id', 'expiring_product.product_id')
      ->leftJoin('hot_product', 'productinfo.id', 'hot_product.product_id')
      ->leftJoin('recommend_product', 'productinfo.id', 'recommend_product.product_id')
      ->leftJoin('spe_price_product', 'productinfo.id', 'spe_price_product.product_id')
      ->leftJoin('kol_productinfo as kp', 'productinfo.id', 'kp.productinfo_id')
      ->leftJoin('kol', 'kol.id', 'kp.kol_id')
      ->leftJoin('productinfo_orders as po', 'po.prod_id', 'productinfo.id')
      ->whereRaw("{$seach_where} {$ck_where} {$cate_serach} {$kol_search} {$online_search} {$productinfo_orders_where}");

    // 添加 category_type 篩選到 target_products 查詢
    $category_type = isset($search_data['category_type']) ? $search_data['category_type'] : null;
    if ($category_type !== null && $category_type !== '') {
      $target_products_query = $target_products_query->where('productinfo.category_type', $category_type);
    }

    $target_products = $target_products_query->groupBy('productinfo.id')->get();
    $target_products = CommonService::objectToArray($target_products);
    $search_result['rowCount'] = count($target_products); /*計算符合條件的商品總數*/

    // dump( $seach_where.$ck_where.$cate_serach.$kol_search.$productinfo_orders_where );
    $productinfo_db = DB::table('productinfo')
      ->select(
        'productinfo.title',
        'productinfo.id',
        'productinfo.order_id',
        'productinfo.top_order_id',
        'productinfo.house_date',
        'productinfo.product_id',
        'productinfo.pic',
        'productinfo.has_price',
        'productinfo.product_cate',
        'productinfo.updatetime',
        'productinfo.online',
        'productinfo.final_array',
        'productinfo.is_registrable',
        'productinfo.ISBN',
        'po.order_id as po_order_id',
        'po.top_order_id as po_top_order_id',
        'expiring_product.product_id as expiring_product',
        'hot_product.product_id as hot_product',
        'recommend_product.product_id as recommend_product',
        'spe_price_product.product_id as spe_price_product',
        'kol.kol_name'
      )
      ->leftJoin('productinfo_type as p_t', 'p_t.product_id', 'productinfo.id')
      ->leftJoin('position_portion', 'productinfo.id', 'position_portion.product_id')
      ->leftJoin('expiring_product', 'productinfo.id', 'expiring_product.product_id')
      ->leftJoin('hot_product', 'productinfo.id', 'hot_product.product_id', 'left')
      ->leftJoin('recommend_product', 'productinfo.id', 'recommend_product.product_id')
      ->leftJoin('spe_price_product', 'productinfo.id', 'spe_price_product.product_id')
      ->leftJoin('kol_productinfo as kp', 'kp.productinfo_id', 'productinfo.id')
      ->leftJoin('kol', 'kol.id', 'kp.kol_id')
      ->leftJoin('productinfo_orders as po', 'po.prod_id', 'productinfo.id')
      ->whereRaw("{$seach_where} {$ck_where} {$cate_serach} {$kol_search} {$online_search} {$productinfo_orders_where}");

    $distributor_id = isset($search_data['distributor_id']) ? $search_data['distributor_id'] : '';
    if ($distributor_id !== '' && $distributor_id !== '-1') { /*依經銷商查看*/
      $productinfo_db = $productinfo_db->whereRaw('productinfo.distributor_id="' . $distributor_id . '"');
    }

    // 添加 category_type 篩選
    $category_type = isset($search_data['category_type']) ? $search_data['category_type'] : null;
    if ($category_type !== null && $category_type !== '') {
      $productinfo_db = $productinfo_db->where('productinfo.category_type', $category_type);
    }

    if ($frontend_view) { // 前台查看(依品像排序篩選顯示漁獵表的價格)
      $first_type_ids = [];
      foreach ($target_products as $key => $value) {
        $first_type = self::get_product_price_option($value);
        $first_type_id = $first_type ? $first_type[0]["idtype"] : "";
        if ($first_type_id != "") {
          array_push($first_type_ids, $first_type_id);
        }
      }
      $first_type_ids = $first_type_ids ? implode(',', $first_type_ids) : "0";
    } else {
      $first_type_ids = '';
    }
    // dump($first_type_ids);exit;
    if ($first_type_ids) {
      $productinfo_db = $productinfo_db->whereRaw('(
      p_t.id in (' . $first_type_ids . ')
      || p_t.id is null
      || p_t.count = 0
      || productinfo.has_price = 0
      ) AND (p_t.online!=0 || p_t.online is null)');
    }

    $productinfo_db = $productinfo_db->groupBy('productinfo.id')
      ->orderByRaw($order_sql)->paginate($per_page_rows)
      ->appends([
        'searchKey' => $searchKey,
        'searchPrev' => $searchPrev,
        'searchBranch' => $searchBranch,
        'ck' => $ck,
        'position_id' => $position_id,
        'position_number' => $position_number,
        'kol_id' => $kol_id,
        'distributor_id' => $distributor_id,
        'act_id' => $act_id,
        'sort' => $sort_method,
        'price_desc' => $price_range_search,
        'product_cate' => $product_cate,
        'bonus_model_id' => $bonus_model_id,
        'category_type' => $category_type,
      ]);
    $search_result['productinfo'] = $productinfo_db;

    $expiring_product = DB::table('expiring_product')->select('product_id')->whereRaw('product_id <> 0')->get();
    $expiring_product = CommonService::objectToArray($expiring_product);
    $hot_product = DB::table('hot_product')->select('product_id')->whereRaw('product_id <> 0')->get();
    $hot_product = CommonService::objectToArray($hot_product);
    $recommend_product = DB::table('recommend_product')->select('product_id')->whereRaw('product_id <> 0')->get();
    $recommend_product = CommonService::objectToArray($recommend_product);
    $spe_price_product = DB::table('spe_price_product')->select('product_id')->whereRaw('product_id <> 0')->get();
    $spe_price_product = CommonService::objectToArray($spe_price_product);

    $productinfoItem = $search_result['productinfo']->items();
    $productinfoItem = CommonService::objectToArray($productinfoItem);
    //dd($productinfoItem);
    foreach ($productinfoItem as $key => $value) {
      $value['pic'] = empty($value['pic']) == true ? [] : json_decode($value['pic'], true);
      $final_array = self::show_array($value['final_array'], 'text');
      $value['show_array'] = implode('<br>', $final_array);
      $value['item'] = '[]';
      if ($value['has_price'] == 1) {
        /*有問題*/
        $value['item'] = json_encode(self::get_price($value['id']));
      }

      $value['expiring_product'] = in_array(
        ['product_id' => $value['id']],
        $expiring_product
      ) ? 1 : 0;

      $value['hot_product'] = in_array(
        ['product_id' => $value['id']],
        $hot_product
      ) ? 1 : 0;

      $value['recommend_product'] = in_array(
        ['product_id' => $value['id']],
        $recommend_product
      ) ? 1 : 0;

      $value['spe_price_product'] = in_array(
        ['product_id' => $value['id']],
        $spe_price_product
      ) ? 1 : 0;

      $productinfoItem[$key] = $value;
    }
    $search_result['productinfoItem'] = $productinfoItem;

    return $search_result;
  }

  /* 回傳商品顯示位置 */
  public static function show_array($array_text = '[]', $type = 'text')
  { //多階層查詢  2019/12/17
    $array = json_decode($array_text, true) ?? [];

    //dump($array);
    $re_array = [];
    foreach ($array as $k => $v) {
      if ($v['prev_id'] != 0) {
        $return = DB::table('product')->select('id', 'title', 'online', 'distributor_id')->find($v['prev_id']);
        $return = CommonService::objectToArray($return);
        if (config('control.control_platform') == 1) {
          $title = empty($return['distributor_id']) == false && $return['distributor_id'] == 0 ? '平台：' : '';
        } else {
          $title = '';
        }
        $title .= ($return['title']) ?? '';
        $re_array[$k] = array($title);
      } else {
        continue;
      }
      if ($v['branch_id'] != 0) {
        $return = DB::table('product')->select('id', 'title', 'online')->find($v['prev_id']);
        $return = CommonService::objectToArray($return);
        $return2 = DB::table('typeinfo')->select('id', 'title', 'online')->find($v['parent_id']);
        $return2 = CommonService::objectToArray($return2);

        if (empty($return2) == false) {
          array_push($re_array[$k], $return2['title']);
        }

        $return3 = DB::table('typeinfo')
          ->select('id', 'title', 'parent_id', 'branch_id', 'online')
          ->find($v['branch_id']);
        $return3 = CommonService::objectToArray($return3);
        $array3 = [];

        if (empty($return3) == false) {
          while ($return3['branch_id'] != 0) {
            //dump($return3);
            array_push($array3, $return3['title']);
            $return3 = DB::table('typeinfo')->select('id', 'title', 'parent_id', 'branch_id', 'online')->find($return3['branch_id']);
            $return3 = CommonService::objectToArray($return3);
          }
        }

        //dump($array3);
        if (empty($array3) == false) {
          for ($i = count($array3) - 1; $i >= 0; $i--) {
            array_push($re_array[$k], $array3[$i]);
          }
        }

        continue;
      }
    }
    // dump($array); dump($type); exit;

    switch ($type) {
      case 'text':
        // dump($re_array);
        $text = [];
        foreach ($re_array as $k => $v) {
          //dump($re_array[$k]);
          foreach ($re_array[$k] as $k2 => $v2) {
            $v2 = str_replace('\/', '/', $v2);
            if (empty($text[$k]))
              $text[$k] = '';
            $text[$k] .= $v2;

            if (!empty($re_array[$k][$k2 + 1]))
              $text[$k] .= ' > ';
          }
        }
        return $text;
        break;

      case 'array':
        return $re_array;
        break;
    }
  }

  /*依商品顯示品項選項*/
  static public function get_product_price_option($item)
  {
    $prod_can_registrable = config('control.control_register') == 1 && $item['is_registrable'] == 1;

    /*取得商品所有可品項*/
    $return_item = self::get_price($item['id']);
    $return_item[0]['subtitle'] = '';
    $return_item[0]['idtype'] = '';
    $return_item[0]['originalPrice'] = '';
    $return_item[0]['has_price'] = isset($return_item[0]['has_price']) ? $return_item[0]['has_price'] : 0;
    $return_item[0]['count'] = isset($return_item[0]['count']) ? $return_item[0]['count'] : 0;
    $need_arrange_price = false;
    if ($item['has_price'] == 0) {
      $return_item[0]['has_price'] = $prod_can_registrable ? Lang::get('不開放報名') : Lang::get('請詢價');
    } else if (empty($return_item)) {
      $return_item[0]['has_price'] = $prod_can_registrable ? Lang::get('報名已截止') : Lang::get('請詢價');
    } else {
      $need_arrange_price = true;
      if (count($return_item) > 0) {
        if ($return_item[0]['count'] == 0) {
          $return_item[0]['has_price'] = $prod_can_registrable ? Lang::get('不開放報名') : Lang::get('請詢價');
          $need_arrange_price = false;
        }
      }
    }
    $return_item[0]['offerPrice'] = '<span class="price">' . $return_item[0]['has_price'] . '</span>';

    if ($need_arrange_price) {
      foreach ($return_item as $sk => $sv) {
        $return_item[$sk]['subtitle'] = $sv['title'];
        $return_item[$sk]['idtype'] = $sv['id'];
        $return_item[$sk]['has_price'] = config('extra.shop.dollar_symbol') . $sv['count'];
        if ($sv['price'] == $sv['count']) {
          $return_item[$sk]['originalPrice']  = '';
        } else {
          $return_item[$sk]['originalPrice']  = config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . number_format($sv['price']);
        }
        $return_item[$sk]['offerPrice']       = config('extra.shop.dollar') . config('extra.shop.dollar_symbol') . '<span class="price">' . number_format($sv['count']) . '</span>';
      }
    }
    // dump($return_item);
    return $return_item;
  }
  /*依商品id取得品項*/
  static public function get_price($productinfo_id, $show_all = false)
  {
    $db = DB::table('productinfo_type as py');
    $db = $db->select('py.*', 'py.num as number')
      ->where('py.product_id', $productinfo_id)
      ->where('py.online', 1)
      ->where('py.closed', 0);
    if (!$show_all) {
      $db = $db->where(function ($query) { /*篩出在期間內的品項*/
        $query->orWhere(function ($query) {
          $query->where('py.start_time', '<=', date('Y-m-d'))
            ->where('py.end_time', '>=', date('Y-m-d'));
        })
          ->orWhere(function ($query) { /*現在時間在開始時間之後，且未設定結束時間*/
            $query->where('py.start_time', '<=', date('Y-m-d'))
              ->where('py.end_time', '=', '');
          })
          ->orWhere(function ($query) { /*現在時間在結束時間之前，且未設定開始時間*/
            $query->where('py.end_time', '>=', date('Y-m-d'))
              ->where('py.start_time', '=', '');
          })
          ->orWhere(function ($query) { /*未設定開始及結束時間*/
            $query->where('py.start_time', '=', '')
              ->where('py.end_time', '=', '');
          })
        ;
      });
    }

    $price = $db->orderByRaw('py.order_id asc, py.id asc')->get();
    $price = CommonService::objectToArray($price);
    // dump($db->getLastSql());
    return $price;
  }

  /*依會員商品的瀏覽等級取得篩選限制*/
  static public function get_user_view_product_limit($user_id)
  {
    $where_user_view_product = '';
    if (empty(config('control.close_function_current')['會員瀏覽商品設定'])) { /*有啟用*/
      $where_user_view_product = 'id=-1';
      $MemberInstance = new MemberInstance($user_id);
      $user_data = $MemberInstance->get_user_data();
      if ($user_data) {
        $prod_ids = DB::connection('main_db')->table('product_view as pv')
          ->select('pvp.prod_id')
          ->join('product_view_product as pvp', 'pvp.view_id', 'pv.id')
          ->where('pv.id', $user_data['product_view_id'])
          ->where('pv.online', 1)
          ->get();
        $prod_ids = CommonService::objectToArray($prod_ids);
        $where_in = [];
        foreach ($prod_ids as $key => $value) {
          array_push($where_in, $value['prod_id']);
        }
        if ($where_in) {
          $where_user_view_product = 'id in (' . implode(',', $where_in) . ')';
        }
      }
    }
    // dump($where_user_view_product);exit;
    return $where_user_view_product;
  }

  /*依商品id標記優惠券*/
  static public function find_prod_coupon($id)
  {
    $coupon_button = '';

    $productinfo = DB::table('productinfo')->find($id);
    $productinfo = CommonService::objectToArray($productinfo);
    if (!$productinfo) {
      return $coupon_button;
    }

    $coupon_confirm = [];

    $coupon_sql = CouponCheck::arrange_search_sql();
    $coupon_confirm = DB::table('coupon')
      ->whereRaw("(area_id='" . $id . "' or area='0') and type='0'")
      ->whereRaw($coupon_sql)
      ->where('distributor_id', $productinfo['distributor_id'])
      ->orderByRaw('area desc,end desc, price')->get();
    $coupon_confirm = CommonService::objectToArray($coupon_confirm);
    if (!empty($coupon_confirm)) {
      if (count($coupon_confirm) > 1) {
        $coupon_button = 'href="/Product/coupon?prod_id=' . $id . '"';
      } else if ($coupon_confirm[0]['price'] == 0) {
        $coupon_button = 'href="javascript:void(0)" onclick="getCoupon(' . $coupon_confirm[0]['id'] . ')"';
      } else {
        $coupon_button = 'href="/Product/couponinfo?id=' . $coupon_confirm[0]['id'] . '"';
      }
    }
    return $coupon_button;
  }
  /*依商品id標記活動or折扣*/
  static public function find_prod_act($id)
  {
    $act_confirm = DB::table('act as a')
      ->join('act_product as ap', 'ap.act_id', 'a.id')
      ->where('ap.prod_id', $id)
      ->whereRaw("a.online = 1
                        AND (
                          a.end <= 0
                          OR
                          ( a.start < " . time() . " AND  a.end > " . time() . ")
                          )")
      ->get();
    $act_confirm = CommonService::objectToArray($act_confirm);
    $act_data = ['act_type' => 0, 'type_name' => '', 'act_button' => '', 'name' => '', 'content' => ''];
    if (!empty($act_confirm)) {
      $act_data['type_name'] = $act_confirm[0]['act_type'] == 1 ? Lang::get('活動') : Lang::get('立馬省');
      $act_data['act_type'] = $act_confirm[0]['act_type'];
      $act_data['link'] = 'href="/Product/activity?id=' . $act_confirm[0]['id'] . '"';
      $act_data['name'] = $act_confirm[0]['name'];
      $act_data['content'] = $act_confirm[0]['content'];
    }
    return $act_data;
  }
  /*依會員優惠券id、數量、加入購物車方式，判斷是否達領取上線*/
  static public function checkCouponNum($coupon_id, $user_id, $num = 0, $cmd = 'assign')
  {
    $coupon = DB::table('coupon_pool')
      ->whereNull('coupon_pool.owner')
      ->select(
        'coupon_pool.id AS id',
        'coupon.price AS price',
        'coupon.title AS title',
        'coupon.limit_num AS limit_num'
      )
      ->whereRaw('(
                          coupon_pool.login_time < ' . (time() - 21600) . ' OR ' . '
                          coupon_pool.login_time IS NULL
                )')
      ->where('coupon.id', $coupon_id)
      ->join('coupon', 'coupon_pool.coupon_id', '=', 'coupon.id')
      ->first();
    $coupon = CommonService::objectToArray($coupon);
    if (!$coupon) {
      return $respData = [
        'code' => 0,
        'msg' => Lang::get('無資料'),
      ];
    }
    $limit_num = $coupon['limit_num']; // 領取數量上限
    //die();

    $coupon_count = DB::table('coupon_pool')
      ->whereRaw("owner='" . $user_id . "' and coupon_id ='" . $coupon_id . "'")
      ->count();
    $coupon_count = $coupon_count + $num; // 已領取的量+量

    if ($cmd == 'increase') { // 用加法，多檢查以加入購物車的量
      $Proposal = Proposal::withTeamMembersAndRequire(
        ['GetCartData'],
        [
          'cart_session' => 'cart_all',
          'user_id' => $user_id,
        ]
      );
      $Proposal = MemberFactory::createNextMember($Proposal);
      $cartData = json_decode($Proposal->projectData['data'], true);
      $incart_num = isset($cartData[$coupon_id . '_coupon']) ? $cartData[$coupon_id . '_coupon'] : 0;
      $coupon_count = $coupon_count + $incart_num;
    }

    if (!session()->get('user.id')) {
      $respData = [
        'code' => 0,
        'msg' => Lang::get('請先登入會員'),
      ];
    } elseif (!$coupon) {
      $respData = [
        'code' => 0,
        'msg' => Lang::get('已結束'),
      ];
    } elseif ($coupon_count > $limit_num) {
      $respData = [
        'code' => 0,
        'msg' => Lang::get('超出上限'),
      ];
    } else {
      $respData = [
        'code' => 1,
        'msg' => Lang::get('操作成功'),
      ];
    }
    return $respData;
  }
  /*依傳入階層檢查 商品是否顯示 及 傳入的麵包屑紀錄是否有效*/
  static public function check_product_and_infotype_close($final_array, $title_array = [])
  {
    $close_final = true; /*商品是否不顯示*/
    $in_title_array = false; /*麵包屑是否存在於勾選的階層中*/

    $final_array = json_decode($final_array);
    foreach ($final_array as $k => $v) {
      $close = false;
      if (count($title_array) > 0 && $in_title_array == false) { /*有提供麵包屑進行比對 且 還未發現存在於勾選的階層中*/
        $last_layer = end($title_array);
        if ($last_layer['type'] == "typeinfo" && $last_layer['id'] == $v->branch_id) {
          $in_title_array = true;
        } else if ($last_layer['type'] == "product" && $last_layer['id'] == $v->prev_id && $v->branch_id == 0) {
          $in_title_array = true;
        }
      }

      $branch_id = $v->branch_id;
      while ($branch_id != 0) { // 由下掛的分類id逐層往上找
        $typeinfo = DB::table('typeinfo')
          ->select('id', 'title', 'parent_id', 'branch_id', 'online')
          ->find($branch_id);
        $typeinfo = CommonService::objectToArray($typeinfo);
        if ($typeinfo['online'] == 2) { // 分類關閉了
          $close = true;
          break;
        } else { // 分類開啟
          $close = false;
          $branch_id = $typeinfo['branch_id'];
        }
      }

      $product = DB::table('product')
        ->select('id', 'title', 'inner_adv01_pic', 'inner_adv01_link', 'online')
        ->find($v->prev_id);
      if ($product->online == 2) { // 分館關閉了
        $close = true;
      }

      /*把先前紀錄的開關狀態跟此分館的開關狀態做聯集，只要有一個分館類有開(close=false)，最終就是要顯示(false)*/
      $close_final = $close_final && $close;
    }
    // dump($close_final);exit;
    $return_data = [
      'close' => $close_final, /*不顯示狀態*/
      'title_array' => $in_title_array ? $title_array : [], /*麵包屑紀錄*/
      'rand_layer' => $final_array[rand(0, count($final_array) - 1)], /*隨機的一個階層*/
    ];
    // dump($in_title_array);exit;
    return (object)$return_data;
  }
}
