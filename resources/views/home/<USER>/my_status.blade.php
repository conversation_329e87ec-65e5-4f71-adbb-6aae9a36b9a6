@extends('home.Public.mainTpl')
@section('title'){{Lang::get('招募會員列表')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <style type="text/css">
        #progress-container {
            position: relative;
            width: 100%;
            height: 10px;
            background-color: #D9D9D9;
            border-radius: 20px;
            overflow: hidden;
        }
        #progress-bar {
            height: 100%;
            background-color:var(--sub-color);
            width: 0;
            border-radius:20px;
            transition: width 0.5s;
        }
        #progress-text {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 1.2em;
        }
        #progress-text span{ color:#707070;font-weight:400;}
        #progress-text .current_price{font-size:18px;color: rgba(0, 0, 0, .87);font-weight: 600;}
        .total_range span{color:var(--sub-color);font-size:16px; font-weight:600;}
        
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Member/down_line')}}">{{Lang::get('會員積分查看')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberMiddle row member_point_box">

                            <div class="col-lg-12 col-12">
                                <div class="d-flex flex-wrap">
                                    <div class="partner_level border col-12 col-lg-6">
                                        <div class="headingBox mb-3">
                                            <h3 class="subtitle">
                                                <span>{{Lang::get('消費圓滿')}}</span>
                                            </h3>
                                            <div class="itembox">
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name text-white">推薦點</div>
                                                    <div class="dollor">{{ number_format($data['consumption_num_added']) }} (美金)</div>
                                                </div>
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name2 text-white">消費點</div>
                                                    <div class="dollor">
                                                        {{ number_format($data['userD']['increasing_limit_consumption']) }}
                                                        (美金)
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12 col-12">
                                <div class="headingBox">
                                    <h3 class="subtitle"><span>{{Lang::get('會員積分')}}</span></h3>
                                </div>
                                <div class="member_point mb-4">
                                    <div class="head">
                                        <h4 class="title">{{Lang::get('目前會員積分')}}</h4>
                                        <div class="select-wrap">
                                            <span class="name">{{Lang::get('幣別')}}：</span>
                                            <select class="form-control" v-model="currency">
                                                <option value="USD">{{Lang::get('美金')}}</option>
                                                <option value="NT">{{Lang::get('台幣')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="cont d-flex flex-wrap">
                                        <div class="item">
                                            <div class="integral">
                                                <div>
                                                    <h4 class="title d-inline-block mr-2">{{Lang::get('增值積分')}}</h4>
                                                    (<a href="{{url('Points/point_increasable')}}">{{Lang::get('查看紀錄')}}</a>)
                                                </div>
                                                <p class="num">{{ number_format($data['userD']['point_increasable'], 3) }}</p>
                                                <span class="unit">1 {{Lang::get('積分')}}=
                                                    <span>{{$data['show_dollar']}}</span>
                                                    {{ number_format($data['pi_value'] * $data['exchange_reate'], 3) }}
                                                </span>
                                            </div>
                                            <p class="equal">= 
                                                <span>{{$data['show_dollar']}}</span>
                                                {{ number_format($data['userD']['point_increasable'] * $data['pi_value'] * $data['exchange_reate']) }}
                                            </p>
                                        </div>
                                        <div class="item">
                                            <div class="integral">
                                                <div>
                                                    <h4 class="title d-inline-block mr-2">{{Lang::get('現金積分')}}</h4>
                                                    (<a href="{{url('Points/points')}}">{{Lang::get('查看紀錄')}}</a>)
                                                </div>
                                                <p class="num">{{ number_format($data['userD']['point'], 3) }}</p>
                                                <span class="unit">1 {{Lang::get('積分')}}=
                                                    <span>{{$data['show_dollar']}}</span>
                                                    {{ number_format(1 * $data['exchange_reate'], 3) }}
                                                </span>
                                            </div>
                                            <p class="equal">=
                                                <span>{{$data['show_dollar']}}</span>
                                                {{ number_format($data['userD']['point'] * $data['exchange_reate']) }}
                                            </p>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>

                            @if($data['partner_level_now'] != null || $data['userD']['partner_level_id'] > 0 || $data['userD']['center_level_id'] > 0 || $data['userD']['vip_type'] > 0)
                            <div class="col-lg-12 col-12">
                                <div class="headingBox">
                                    <h3 class="subtitle"><span>{{Lang::get('合夥人等級')}}</span></h3>
                                </div>
                                <div class="d-flex flex-wrap">
                                    <div class="partner_level border col-12 col-lg-6">
                                        <div class="head d-flex flex-wrap justify-content-between align-items-start">
                                            <div class="title-box">
                                                <h4 class="title mb-2">目前合夥人等級 :<span class="ml-2">{{$data['partner_level_now']['name'] ?? '無'}}</span></h4>
                                                <!-- <p class="total mb-4">已累積投資 : 
                                                    <span>{{ number_format($data['userD']['partner_accumulation']) }}</span>
                                                    <span class="currency">(美金)</span>
                                                </p> -->
                                            </div>
                                            <div class="form-check form-check-inline mb-2">
                                                <input class="form-check-input d-none" type="checkbox" id="auto_partner" 
                                                       true-value="2" false-value="1" v-model="auto_partner">
                                                <label class="form-check-label" for="auto_partner">自動升級合夥人等級</label>
                                            </div>
                                        </div>
                                        <div class="content mb-3">
                                            <div class="d-flex justify-content-between mb-2 total_point">
                                                <h3>已累積功德</h3>
                                                <p class="point">功德圓滿點數: 
                                                    <br>
                                                    @if($data['userD']['increasing_limit_invest'] < 0)
                                                        累積自動升級中...
                                                    @else
                                                        {{ number_format($data['userD']['increasing_limit_invest']) }}
                                                        <span class="currency">(美金)</span>
                                                    @endif
                                                </p>
                                            </div>
                                            <div id="progress-text">
                                                <span id="current_price" class="current_price" data-current="{{ $data['userD']['partner_accumulation']?? 0 }}">0</span> / 
                                                <span id="total_price" class="total_price" data-total="{{ $data['partner_level_next']['contribution']?? 0 }}">0</span><span class="currency">(美金)</span>
                                            </div>
                                            <div id="progress-container">
                                                <div id="progress-bar"></div>
                                            </div>
                                            <p class="total_range mt-2">
                                                @if($data['partner_level_next'])
                                                    還差 
                                                    <span id="difference" class="difference">0</span>
                                                    <span class="currency">(美金)</span>
                                                    可晉升下一個<span class="label">【{{ $data['partner_level_next']['name']?? '' }}】</span>等級 !
                                                @else
                                                    已達最高等級 !
                                                @endif
                                            </p>

                                        </div>
                                        <div class="headingBox mb-3">
                                            <h3 class="subtitle"><span>{{Lang::get('功德圓滿')}}</span></h3>
                                            <div class="itembox">
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name  text-white">已增值</div>
                                                    <div class="dollor">{{ number_format($data['invest_num_added']) }} <span class="currency">(美金)</span></div>
                                                </div>
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name2  text-white">可增值</div>
                                                    <div class="dollor">
                                                        @if($data['userD']['increasing_limit_invest'] < 0)
                                                            累積自動升級中...
                                                        @else
                                                            {{ number_format($data['userD']['increasing_limit_invest']) }} 
                                                            <span class="currency">(美金)</span>
                                                        @endif
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="headingBox mb-3">
                                            <h3 class="subtitle">
                                                <span>{{Lang::get('消費圓滿')}}</span>
                                            </h3>
                                            <div class="itembox">
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name text-white">已增值</div>
                                                    <div class="dollor">{{ number_format($data['consumption_num_added']) }} (美金)</div>
                                                </div>
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name2 text-white">可增值</div>
                                                    <div class="dollor">
                                                        {{ number_format($data['userD']['increasing_limit_consumption']) }}
                                                        (美金)
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="headingBox mb-3">
                                            <h3 class="subtitle">
                                                <span>{{Lang::get('其它圓滿')}}</span>
                                            </h3>
                                            <div class="itembox">
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name text-white">已增值</div>
                                                    <div class="dollor">{{ number_format($data['other_num_added']) }} (美金)</div>
                                                </div>
                                                <div class="item w-100 m-2 d-flex justify-content-between align-items-center">
                                                    <div class="name2 text-white">可增值</div>
                                                    <div class="dollor">
                                                        {{ number_format($data['userD']['increasing_limit_other']) }}
                                                        (美金)
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>  
                                    <div class="level_point  col-12 col-lg-6">
                                        <div class="thead">
                                            <div class="item">等級</div>
                                            <div class="item text-center">需求CV<span class="currency">(美金)</span></div>
                                            <div class="item text-center">功德圓滿倍率</div>
                                        </div>
                                        <div class="cont">
                                            <div class="item @if(0==$data['userD']['partner_level_id']) active @endif">
                                                <div class="name">尚未晉級</div>
                                                <div class="price">-</div>
                                                <div class="ratio">{{$data['partner_level_first']['ratio'] ?? ''}}</div>
                                            </div>
                                            @foreach($data['arr_partner_levels'] as $id=>$level)
                                            <div class="item @if($id==$data['userD']['partner_level_id']) active @endif">
                                                <div class="name">{{$level['name']}}</div>
                                                <div class="price">{{ number_format($level['contribution']) }}</div>
                                                <div class="ratio">{{$level['ratio']}}</div>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>  
                                </div>
                            </div>
                            @endif
                        </div>
                        <!-- //////////////////////////////////// -->
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    <script>
        function formatCurrency(value) {
            return new Intl.NumberFormat('zh-Hans-CN', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
            }).format(value);
        }
        function updateProgress(current) {
            const currentElement = document.getElementById('current_price');
            const totalElement = document.getElementById('total_price');
            const differenceElement = document.getElementById('difference');
            const current_price = parseInt(currentElement.getAttribute('data-current'), 10);
            const total_price = parseInt(totalElement.getAttribute('data-total'), 10);
            const difference = total_price - current_price;
            NProgress.start();
            const percentage = (current_price / total_price) * 100;
            document.getElementById('progress-bar').style.width = percentage + '%';
            
        
            currentElement.textContent = formatCurrency(current_price);
            totalElement.textContent = formatCurrency(total_price);
            differenceElement.textContent = formatCurrency(difference);
            
            // 完成进度条
            NProgress.done();
        }

        window.onload = () => {
            updateProgress();
        };

        var rightContentBox_data = {
            currency: "{{ $data['currency_selected'] }}",
            auto_partner: "{{$data['userD']['auto_partner']}}",
            exchange_rate_set: JSON.parse('{!! json_encode(config('extra.skychakra.exchange_rate_set')) !!}'),
        };
        var rightContentBoxVM  = new Vue({
            el: '#rightContentBox',
            data: rightContentBox_data,
            watch:{
                auto_partner: async function(nv, ov){
                    resp = await $.ajax({
                        type: "POST",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: "json",
                        data:{
                            auto_partner: nv,
                        },
                        url: "{{url('Dividend/set_auto_partner')}}",
                    });
                    Vue.toasted.show(resp.msg, resp.code==1 ? vt_success_obj : vt_error_obj);
                },
                currency: function(nv, ov){
                    if(nv!=ov){
                        keys = Object.keys(this.exchange_rate_set);
                        if(keys.indexOf(nv)!=-1){
                            if(location.search){
                                redirect_url = location.href + '&currency='+nv;
                            }else{
                                redirect_url = location.href + '?currency='+nv;
                            }
                            location.href = redirect_url;
                        }
                    }
                },
            },
            methods:{
            },
        })
    </script>
@endsection

