@extends('home.Public.mainTpl')
@section('title'){{$data['main_layer']['title']}} - {{$data['frontend_menu'][strtolower($data['controller'])]['name']}} | {{$data['seo'][0]['title']}}@endsection

@section('Keywords'){{$data['main_layer']['webtype_keywords']}}@endsection
@section('description'){{$data['main_layer']['webtype_description']}}@endsection

@section('css')@endsection

@section('content')
<style>
.soldCountBox {
    margin-top: 5px;
}
.soldCount {
    font-size: 12px;
    color: #666;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}
</style>
    @if (empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu'][strtolower($data['controller'])]['pic'])
            <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu'][strtolower($data['controller'])]['pic']}});">
                <div class="container">
                    <h2 class="page-title" style="color:{{$data['frontend_menu'][strtolower($data['controller'])]['text_color']}}">
                        @if(!empty($data['product']))
                            {{$data['frontend_menu'][strtolower($data['controller'])]['name']}}
                        @else
                            {{$data['main_layer']['title']}}
                            {{-- <a href="{{url('About/about_contact')}}?distributor_id={{$data['main_layer']['id']}}" target="_blank">
                                <button class="btn">{{Lang::get('聯絡')}}</button>
                            </a>
                            <a href="javascript:register_product({{$data['main_layer']['id']}})">
                                <button class="btn">{{Lang::get('商品註冊')}}</button>
                            </a> --}}
                        @endif
                    </h2>
                    <!-- <span class="enText">{{$data['frontend_menu'][strtolower($data['controller'])]['en_name']}}</span> -->
                </div>
            </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a class="cursor-initial">{{$data['frontend_menu'][strtolower($data['controller'])]['name']}}</a></li>
                @foreach($data['title_array'] as $ta)
                    <li><a href="{{url('Product/'.$ta['type'])}}?id={{$ta['id']}}">{{$ta['title']}}</a></li>
                @endforeach
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                <!-- Side Product Menu -->
                @include('home.Public.productMenu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox">
                <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
                <!-- announcement start -->
                @include('home.Public.newsLink')
                <!-- announcement end -->
                <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
                @if(!empty($data['product']))
                    <div class="">
                        @if($data['product']['inner_adv01_pic'] != '')
                        <a href="{{($data['product']['inner_adv01_link']) ?? '#!'}}">
                            <img src="{{__PUBLIC__}}{{$data['product']['inner_adv01_pic']}}" alt="">
                        </a>
                        @endif
                        @if($data['product']['inner_adv02_pic'] != '')
                        <a href="{{($data['product']['inner_adv02_link']) ?? '#!'}}">
                            <img src="{{__PUBLIC__}}{{$data['product']['inner_adv02_pic']}}" alt="">
                        </a>
                        @endif
                    </div>
                @endif
                <!-- ///////////////////////////////////////////////////////////////////////////////////// -->

                <!-- 本層 -->
                <div class="proBrandZone mt-0">
                    <div class="titleBox">
                        <div class="title">
                            <h3>{{App\Services\CommonService::fat_index_text($data['main_layer']['title'], 15)}}</h3>
                        </div>
                    </div>
                    <div class="titleBrandBox">
                        <div class="leftBox">
                            <div class="d-flex flex-wrap justify-content-between align-items-center ml-2 mr-2">
                                <span>
                                    <!-- {{Lang::get('商品數')}}： {{$data['rowCount']}} -->
                                    @if(empty(config('control.close_function_current')['會員瀏覽商品設定']))({{Lang::get('請留意您的瀏覽權限')}})@endif
                                </span>
                                @include('home.product.search_setting')
                            </div>
                        </div>
                    </div>
                    @if(!empty($data['productinfo']->items()))
                        <!-- start 此分類商品 -->
                        <div class="proBox proItemBox">
                            @if(empty($data['productinfo']->items()) == false)
                            @foreach($data['productinfo']->items() as $k => $productvo)
                                <div class="item">
                                    <div class="img_container">
                                        <div class="img" style="background-image: url({{__PUBLIC__}}{{$productvo->pic1}});"></div>
                                        @if(in_array($productvo->id, $data['store_products']))
                                            <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(0, {{$productvo->id}})">
                                                <i class="bi bi-heart-fill"></i>
                                            </a>
                                        @else
                                            <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(1, {{$productvo->id}})">
                                                <i class="bi bi-heart"></i>
                                            </a>
                                        @endif
                                        @if(!empty($data['coupon_button'][$productvo->id]))
                                            <a class="couponLabel" {{$data['coupon_button'][$productvo->id] ?? ''}}>
                                                <p><span>{{Lang::get('優惠券')}}</span></p>
                                            </a>
                                        @endif
                                    </div>
                                    <div class="img_container addcart_area">
                                        <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                            <div class="img"></div>
                                        </a>
                                        <div class="addcarBox d-none d-lg-block">
                                            @if(!empty($productvo->show[0]['idtype']))
                                                <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                    {{Lang::get('加入購物車')}}
                                                </a>
                                             @endif
                                        </div>
                                    </div>
                                    <div class="textBox">
                                        <h3>
                                            <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                {{Str::limit($productvo->title,40)}}
                                            </a>
                                            @if(isset($data['act_button'][$productvo->id]['act_data']['link']))
                                                <span class="activityLabel">
                                                    <a {{($data['act_button'][$productvo->id]['act_data']['link']) ?? '' }}>{{$data['act_button'][$productvo->id]['act_data']['type_name']}}</a>
                                                </span>
                                            @endif
                                        </h3>
                                        <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                            <div class="priceBox">
                                                <span class="originalPrice">{{$productvo->show[0]['originalPrice']}}</span>
                                                <span class="offerPrice">{!! $productvo->show[0]['offerPrice'] !!}</span>
                                            </div>
                                        </a>
                                        <div class="addcarBox d-block d-lg-none">
                                            @if(isset($productvo->show[0]['idtype']))
                                                <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                    {{Lang::get('加入購物車')}}
                                                </a>
                                             @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            @endif
                        </div>
                        <!-- end 此分類商品 -->
                    @endif
                </div>

                <!-- proBrandZone start 子分類商品-->
                @foreach($data['subProduct'] as $vo)
                    @if(!empty($vo['productinfo']['data']))
                        <div class="proBrandZone">
                            <div class="titleBox">
                                <div class="title">
                                    <h3><a href="{{url('Product/typeinfo')}}?id={{$vo['id']}}">{{App\Services\CommonService::fat_index_text($vo['title'], 15)}}</a></h3>
                                </div>
                            </div>
                            <div class="proBox">
                                <!-- /////////////////////////////////// -->
                                <div class="owl-carousel proImgCarousel owl-theme">
                                    @foreach($vo['productinfo']->items() as $productvo)
                                        <div class="item">
                                            <div class="img_container">
                                                <div class="img" style="background-image: url({{__PUBLIC__}}/{$productvo->pic1});"></div>
                                                @if(in_array($productvo->id, $data['store_products']))
                                                    <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(0, {{$productvo->id}})">
                                                        <i class="bi bi-heart-fill"></i>
                                                    </a>
                                                @else
                                                    <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(1, {{$productvo->id}})">
                                                        <i class="bi bi-heart"></i>
                                                    </a>
                                                @endif
                                                @if(isset($data['coupon_button'][$productvo->id]))
                                                    <a class="couponLabel" {{ ($data['coupon_button'][$productvo->id]) ?? '' }}>
                                                        <p><span>{{Lang::get('優惠券')}}</span></p>
                                                    </a>
                                                @endif
                                            </div>
                                            <div class="img_container addcart_area">
                                                <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                    <div class="img"></div>
                                                </a>
                                                <div class="addcarBox d-none d-lg-block">
                                                    @if(isset($productvo->show[0]['idtype']))
                                                        <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                            {{Lang::get('加入購物車')}}
                                                        </a>
                                                     @endif
                                                </div>
                                            </div>
                                            <div class="textBox">
                                                <h3>
                                                    <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                        {{App\Services\CommonService::fat_index_text($productvo->title, 40)}}
                                                    </a>
                                                    @if(!empty($data['act_button'][$productvo->id]['act_data']['link']))
                                                        <span class="activityLabel">
                                                            <a {{$data['act_button'][$productvo->id]['act_data']['link'] ?? '' }}>{{$data['act_button'][$productvo->id]['act_data']['type_name']}}</a>
                                                        </span>
                                                    @endif
                                                </h3>
                                                <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                    <div class="priceBox">
                                                        <span class="originalPrice">{{$productvo->show[0]['originalPrice']}}</span>
                                                        <span class="offerPrice">{{$productvo->show[0]['offerPrice']}}</span>
                                                    </div>
                                                    @if(isset($productvo->sold_count_display) && $productvo->sold_count_display > 0)
                                                        <div class="soldCountBox">
                                                            <span class="soldCount">已售出 {{ \App\Services\SoldCountService::formatSoldCount($productvo->sold_count_display) }}</span>
                                                        </div>
                                                    @endif
                                                </a>
                                                <div class="addcarBox d-block d-lg-none">
                                                    @if(!empty($productvo->show[0]['idtype']))
                                                        <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                            {{Lang::get('加入購物車')}}
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <!-- /////////////////////////////////// -->
                            </div>
                        </div>
                    @endif
                @endforeach
                <!-- proBrandZone end 子分類商品-->

                <div class="row paginationBox">
                    <div class="col-12 boxCenter">
                        {{$data['productinfo']->links('pagination::default')}}
                    </div>
                </div>
                <!-- ///////////////////////////////////////////////////////////////////////////////////// -->
                @if(count($data['productRand'])>0)
                    <!-- <div class="popularProBranch">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('其它熱門分類館')}}</h3>
                            </div>
                        </div>
                        <div class="row">
                            @foreach($data['productRand'] as $productRandvo)
                                <div class="col-md-6 recommendBox">
                                    <div>
                                        <img src="{{__PUBLIC__}}{{$productRandvo['pic']}}" alt="">
                                        <p>{!! $productRandvo['content'] !!}</p>
                                        <a href="{{url('Product/product')}}?id={{$productRandvo['id']}}" class="togo">
                                            {{Lang::get('立即前往')}}<i class="icon-right"></i><span>{{$productRandvo['title']}}</span>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div> -->
                    @endif
                </div>
                <!-- ///////////////////////////////////////////////////////////////////////////////////// -->
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/owl.productBrandAll.js"></script>
    @include('home.product.search_setting_js')
@endsection
