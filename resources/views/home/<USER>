@extends('home.Public.mainTpl')

@section('title', '商品分類測試')

@section('content')
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>商品分類測試頁面</h2>
            <p>測試 category_type 功能，可以切換顯示商城分類或課程分類</p>
            
            <!-- 分類切換按鈕 -->
            <div class="btn-group mb-4" role="group">
                <button type="button" class="btn btn-primary" id="btn-shop" onclick="switchCategoryType(0)">
                    商城分類 (category_type = 0)
                </button>
                <button type="button" class="btn btn-secondary" id="btn-course" onclick="switchCategoryType(1)">
                    課程分類 (category_type = 1)
                </button>
                <button type="button" class="btn btn-info" id="btn-all" onclick="switchCategoryType(null)">
                    全部分類 (不篩選)
                </button>
            </div>
            
            <!-- 顯示當前選擇的分類類型 -->
            <div class="alert alert-info">
                <strong>當前分類類型：</strong><span id="current-type">全部分類</span>
            </div>
            
            <!-- 商品選單顯示區域 -->
            <div class="row">
                <div class="col-md-6">
                    <h4>左側商品選單 (getProdAsideMenu)</h4>
                    <div id="aside-menu" class="border p-3" style="min-height: 300px;">
                        <div v-for="item in sideProductMenu" class="mb-2">
                            <div class="font-weight-bold">{{ item.title }}</div>
                            <div v-if="item.subType && item.subType.length > 0" class="ml-3">
                                <div v-for="sub in item.subType" class="text-muted">
                                    - {{ sub.title }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h4>產品選單 (productMenu)</h4>
                    <div id="product-menu" class="border p-3" style="min-height: 300px;">
                        <div v-for="item in productMenu" class="mb-2">
                            <div class="font-weight-bold">{{ item.title }}</div>
                            <div v-if="item.subType && item.subType.length > 0" class="ml-3">
                                <div v-for="sub in item.subType" class="text-muted">
                                    - {{ sub.title }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 預設資料顯示 -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <h4>商城分類選單 (預設)</h4>
                    <div class="border p-3">
                        @if(isset($data['recommend_product_nav_shop']))
                            @foreach($data['recommend_product_nav_shop'] as $item)
                                <div class="mb-2">
                                    <strong>{{ $item['title'] }}</strong>
                                    @if(isset($item['subType']) && count($item['subType']) > 0)
                                        <div class="ml-3">
                                            @foreach($item['subType'] as $sub)
                                                <div class="text-muted">- {{ $sub['title'] }}</div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <p class="text-muted">無商城分類資料</p>
                        @endif
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h4>課程分類選單 (預設)</h4>
                    <div class="border p-3">
                        @if(isset($data['recommend_product_nav_course']))
                            @foreach($data['recommend_product_nav_course'] as $item)
                                <div class="mb-2">
                                    <strong>{{ $item['title'] }}</strong>
                                    @if(isset($item['subType']) && count($item['subType']) > 0)
                                        <div class="ml-3">
                                            @foreach($item['subType'] as $sub)
                                                <div class="text-muted">- {{ $sub['title'] }}</div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <p class="text-muted">無課程分類資料</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('ownJS')
<script>
// 測試用的 Vue 實例
var testVM = new Vue({
    el: '#aside-menu',
    data: {
        sideProductMenu: []
    }
});

var testProductVM = new Vue({
    el: '#product-menu',
    data: {
        productMenu: []
    }
});

// 切換分類類型的函數
function switchCategoryType(categoryType) {
    // 更新按鈕狀態
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-secondary');
    });
    
    let currentTypeText = '';
    if (categoryType === 0) {
        document.getElementById('btn-shop').classList.remove('btn-secondary');
        document.getElementById('btn-shop').classList.add('btn-primary');
        currentTypeText = '商城分類 (category_type = 0)';
    } else if (categoryType === 1) {
        document.getElementById('btn-course').classList.remove('btn-secondary');
        document.getElementById('btn-course').classList.add('btn-primary');
        currentTypeText = '課程分類 (category_type = 1)';
    } else {
        document.getElementById('btn-all').classList.remove('btn-secondary');
        document.getElementById('btn-all').classList.add('btn-primary');
        currentTypeText = '全部分類 (不篩選)';
    }
    
    document.getElementById('current-type').textContent = currentTypeText;
    
    // 調用 API 更新選單
    updateAsideMenu(categoryType);
    updateProductMenu(categoryType);
}

// 更新左側選單
function updateAsideMenu(categoryType) {
    const distributor_id = "{{request()->get('distributor_id') ?? 0}}";
    var requestData = {
        action: "{{$data['action'] ?? 'product'}}",
        id: "{{request()->get('id') ?? ''}}",
    };
    
    if(categoryType !== null && categoryType !== undefined) {
        requestData.category_type = categoryType;
    }
    
    $.ajax({
        type: "POST",
        headers: {
            'X-CSRF-Token': $('meta[name="_token"]').attr('content')
        },
        dataType: "json",
        url: "{{url('Ajax/getProdAsideMenu')}}?distributor_id="+distributor_id,
        data: requestData,
        success: function (data) {
            testVM.sideProductMenu = data;
            console.log('Aside Menu Data:', data);
        },
        error: function(e) {
            console.error('Error loading aside menu:', e);
        }
    });
}

// 更新產品選單
function updateProductMenu(categoryType) {
    var requestData = {
        action: "{{$data['action'] ?? 'product'}}",
        id: "{{request()->get('id') ?? ''}}",
    };
    
    if(categoryType !== null && categoryType !== undefined) {
        requestData.category_type = categoryType;
    }
    
    $.ajax({
        type: "POST",
        headers: {
            'X-CSRF-Token': $('meta[name="_token"]').attr('content')
        },
        dataType: "json",
        data: requestData,
        url: "{{url('Ajax/productMenu')}}",
        success: function (data) {
            testProductVM.productMenu = data;
            console.log('Product Menu Data:', data);
        },
        error: function(e) {
            console.error('Error loading product menu:', e);
        }
    });
}

// 頁面載入時預設顯示全部分類
$(document).ready(function() {
    switchCategoryType(null);
});
</script>
@endsection
